<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
// TODO: Ambil data kategori dari database
include __DIR__ . '/inc/header.php';
include_once __DIR__ . '/data/category.php';
$categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Daftar Kategori</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Daftar Kategori</h2>
    <a href="category-add.php" class="btn btn-primary mb-3">Tambah Kategori</a>
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>No</th>
                <th><PERSON><PERSON></th>
                <th>Aksi</th>
            </tr>
        </thead>
        <tbody>
        <?php if (is_array($categories) && count($categories) > 0): $no=1; foreach ($categories as $cat): ?>
            <tr>
                <td><?= $no++ ?></td>
                <td><?= htmlspecialchars($cat['category'] ?? '-') ?></td>
                <td>
                    <a href="category-edit.php?id=<?= intval($cat['id']) ?>" class="btn btn-sm btn-warning">Edit</a>
                    <a href="category-delete.php?id=<?= intval($cat['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Yakin hapus kategori?')">Hapus</a>
                </td>
            </tr>
        <?php endforeach; else: ?>
            <tr><td colspan="3" class="text-center">Belum ada kategori.</td></tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
</body>
</html>
