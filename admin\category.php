<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handler
include_once __DIR__ . '/data/category.php';

// Ambil data kategori dari database
try {
    $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
} catch (Exception $e) {
    $categories = [];
    $error_message = "Error mengambil data kategori: " . $e->getMessage();
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-tags mr-3"></i>Manajemen Kategori
        </h1>
        <a href="category-add.php" class="btn btn-brand-info">
            <i class="fa fa-plus mr-2"></i>Tambah Kategori
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fa fa-check-circle mr-2"></i><?= htmlspecialchars($_GET['success']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_GET['error']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-tags mr-2"></i>Daftar Kategori (<?= count($categories) ?> kategori)
        </div>
        <div class="card-body p-0">
            <?php if (count($categories) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: rgba(23,162,184,0.1);">
                        <tr>
                            <th><i class="fa fa-hashtag mr-1"></i>No</th>
                            <th><i class="fa fa-tag mr-1"></i>Nama Kategori</th>
                            <th><i class="fa fa-info-circle mr-1"></i>Deskripsi</th>
                            <th><i class="fa fa-newspaper-o mr-1"></i>Jumlah Post</th>
                            <th><i class="fa fa-calendar mr-1"></i>Dibuat</th>
                            <th><i class="fa fa-cogs mr-1"></i>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($categories as $cat): ?>
                        <?php
                        // Hitung jumlah post per kategori
                        try {
                            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM posts WHERE category_id = ?");
                            $stmt->execute([$cat['id']]);
                            $post_count = $stmt->fetchColumn();
                        } catch (Exception $e) {
                            $post_count = 0;
                        }
                        ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td>
                                <strong><?= htmlspecialchars($cat['category'] ?? '-') ?></strong>
                            </td>
                            <td>
                                <?php
                                $desc = $cat['description'] ?? '';
                                echo htmlspecialchars(strlen($desc) > 50 ? substr($desc, 0, 50) . '...' : $desc);
                                ?>
                            </td>
                            <td>
                                <span class="badge badge-info px-2 py-1">
                                    <?= $post_count ?> post
                                </span>
                            </td>
                            <td>
                                <?= isset($cat['created_at']) ? date('d/m/Y', strtotime($cat['created_at'])) : '-' ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="category-edit.php?id=<?= $cat['id'] ?>" class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <?php if ($post_count == 0): ?>
                                    <a href="category-delete.php?id=<?= $cat['id'] ?>" class="btn btn-sm btn-danger"
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus kategori ini?')" title="Hapus">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                    <?php else: ?>
                                    <button class="btn btn-sm btn-secondary" disabled title="Tidak dapat dihapus karena masih ada postingan">
                                        <i class="fa fa-lock"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fa fa-tags" style="font-size: 4rem; color: #ccc;"></i>
                <h4 class="mt-3 text-muted">Belum ada kategori</h4>
                <p class="text-muted">Klik tombol "Tambah Kategori" untuk membuat kategori baru.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // Add hover effects
    $('tbody tr').hover(
        function() { $(this).addClass('table-active'); },
        function() { $(this).removeClass('table-active'); }
    );
});
</script>

</body>
</html>
