<?php
// <PERSON><PERSON> session dan cek login
session_start();
if (!isset($_SESSION['username'])) {
    header('Location: login.php');
    exit();
}

// Data profil dummy, bisa diganti dengan query database
$username = htmlspecialchars($_SESSION['username']);
$email = isset($_SESSION['email']) ? htmlspecialchars($_SESSION['email']) : '<EMAIL>';
$role = isset($_SESSION['role']) ? htmlspecialchars($_SESSION['role']) : 'Anggota';
$foto = isset($_SESSION['foto']) && $_SESSION['foto'] ? $_SESSION['foto'] : 'images/all-icon/user.png';

// Include header/navbar
include 'inc/navbar.php';
?>

<style>
body {
    background: #fff !important;
    min-height: 100vh;
}
/* Responsive header/navbar */
@media (max-width: 991px) {
    .navbar-nav { flex-direction: column !important; align-items: flex-start !important; }
    .navbar-nav .nav-item { margin-left: 0 !important; margin-bottom: 8px; }
    .navbar-brand img { max-width: 120px; height: auto; }
    footer, .footer { text-align: center !important; padding: 18px 6px !important; font-size: 0.98rem; }
}
</style>

<div class="container mt-5 mb-5">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card shadow-lg border-0 rounded-lg">
                <div class="card-header bg-primary text-white text-center">
                    <h3 class="mb-0">Profil Pengguna</h3>
                </div>
                <div class="card-body p-4">
                    <div class="row align-items-center">
                        <div class="col-md-4 text-center">
                            <img src="<?php echo $foto; ?>" alt="User" class="img-fluid rounded-circle mb-3" style="width: 120px; height: 120px; object-fit: cover; border: 4px solid #007bff;">
                        </div>
                        <div class="col-md-8">
                            <table class="table table-borderless mb-4">
                                <tr>
                                    <th>Username</th>
                                    <td>: <?php echo $username; ?></td>
                                </tr>
                                <tr>
                                    <th>Email</th>
                                    <td>: <?php echo $email; ?></td>
                                </tr>
                                <tr>
                                    <th>Role</th>
                                    <td>: <?php echo $role; ?></td>
                                </tr>
                            </table>
                            <hr>
                            <h5>Edit Akun</h5>
                            <form action="php/edit_profile.php" method="post" enctype="multipart/form-data">
                                <div class="form-group mb-2">
                                    <label for="username">Nama Pengguna</label>
                                    <input type="text" class="form-control" id="username" name="username" value="<?php echo $username; ?>" required>
                                </div>
                                <div class="form-group mb-2">
                                    <label for="password">Password Baru <small class="text-muted">(Kosongkan jika tidak ingin ganti)</small></label>
                                    <input type="password" class="form-control" id="password" name="password" placeholder="Password baru">
                                </div>
                                <div class="form-group mb-3">
                                    <label for="foto">Foto Profil <small class="text-muted">(jpg/png, max 2MB)</small></label>
                                    <input type="file" class="form-control-file" id="foto" name="foto" accept="image/*">
                                </div>
                                <button type="submit" class="btn btn-success">Simpan Perubahan</button>
                            </form>
                        </div>
                    </div>
                </div>
                <div class="card-footer text-center bg-light">
                    <a href="index.php" class="btn btn-outline-primary mr-2">Kembali ke Beranda</a>
                    <a href="php/logout.php" class="btn btn-danger">Logout</a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'inc/footer.php'; ?>
