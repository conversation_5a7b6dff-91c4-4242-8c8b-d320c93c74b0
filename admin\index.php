<?php
include __DIR__ . '/inc/header.php';
$key = "hhdsfs1263z";
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}
// Load data handler
include_once __DIR__ . '/data/pengurus.php';
include_once __DIR__ . '/data/user.php';
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';
include_once __DIR__ . '/data/comment.php';
// DB connection
include_once __DIR__ . '/../db_conn.php';

// Ambil data
$pengurus = getAllPengurus($conn);
$users = getAllUsers($conn);
$posts = getAllPosts($conn);
$categories = getAllCategories($conn);
$comments = getAllComments($conn);

// Ambil data admin
$admin = null;
if (isset($_SESSION['username'])) {
    $stmt = $conn->prepare("SELECT * FROM admin WHERE username = ? LIMIT 1");
    $stmt->execute([$_SESSION['username']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - UKM Panahan Gendewa Geni</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin-custom.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Teko:wght@300..700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="dashboard-content">
        <h1 class="welcome-header mb-4">
            <i class="fa fa-dashboard mr-3"></i>Dashboard Admin - Selamat Datang <?= isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'Admin' ?>
        </h1>
        <div class="row mb-4">
            <div class="col-md-2 col-6 mb-3">
                <div class="card admin-card text-center p-3">
                    <div class="admin-icon icon-pengurus"><i class="fa fa-user-tie"></i></div>
                    <div class="summary-title">Pengurus</div>
                    <div class="mb-3">Jumlah: <b><?= count($pengurus) ?></b></div>
                    <a href="pengurus.php" class="btn btn-brand-primary btn-sm">
                        <i class="fa fa-cogs mr-1"></i>Kelola
                    </a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-3">
                <div class="card admin-card text-center p-3">
                    <div class="admin-icon icon-users"><i class="fa fa-users"></i></div>
                    <div class="summary-title">Pengguna</div>
                    <div class="mb-3">Jumlah: <b><?= count($users) ?></b></div>
                    <a href="users.php" class="btn btn-brand-success btn-sm">
                        <i class="fa fa-user-plus mr-1"></i>Kelola
                    </a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-3">
                <div class="card admin-card text-center p-3">
                    <div class="admin-icon icon-posts"><i class="fa fa-newspaper-o"></i></div>
                    <div class="summary-title">Postingan</div>
                    <div class="mb-3">Jumlah: <b><?= count($posts) ?></b></div>
                    <a href="post.php" class="btn btn-brand-warning btn-sm">
                        <i class="fa fa-edit mr-1"></i>Kelola
                    </a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-3">
                <div class="card admin-card text-center p-3">
                    <div class="admin-icon icon-categories"><i class="fa fa-tags"></i></div>
                    <div class="summary-title">Kategori</div>
                    <div class="mb-3">Jumlah: <b><?= count($categories) ?></b></div>
                    <a href="category.php" class="btn btn-brand-info btn-sm">
                        <i class="fa fa-folder-open mr-1"></i>Kelola
                    </a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-3">
                <div class="card admin-card text-center p-3">
                    <div class="admin-icon icon-comments"><i class="fa fa-comments"></i></div>
                    <div class="summary-title">Komentar</div>
                    <div class="mb-3">Jumlah: <b><?= count($comments) ?></b></div>
                    <a href="comment.php" class="btn btn-secondary btn-sm" style="background: linear-gradient(45deg, #6c757d, #868e96); border: none; color: #fff; border-radius: 25px; font-weight: 600;">
                        <i class="fa fa-comment mr-1"></i>Kelola
                    </a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-3">
                <div class="card admin-card text-center p-3">
                    <div class="admin-icon icon-logout"><i class="fa fa-sign-out"></i></div>
                    <div class="summary-title">Logout</div>
                    <div class="mb-3">&nbsp;</div>
                    <a href="../php/logout.php" class="btn btn-brand-danger btn-sm" onclick="return confirm('Apakah Anda yakin ingin logout?')">
                        <i class="fa fa-power-off mr-1"></i>Logout
                    </a>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card summary-card">
                    <div class="card-header card-header-brand-primary">
                        <i class="fa fa-user-tie mr-2"></i>Tabel Ringkas Pengurus
                    </div>
                    <div class="card-body p-3">
                        <div class="table-responsive">
                        <table class="table table-sm summary-table mb-0">
                            <thead><tr><th><i class="fa fa-user mr-1"></i>Nama</th><th><i class="fa fa-briefcase mr-1"></i>Jabatan</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($pengurus,0,5) as $p): ?>
                                <tr><td><?= htmlspecialchars($p['nama'] ?? '-') ?></td><td><?= htmlspecialchars($p['jabatan'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            <?php if(count($pengurus) == 0): ?>
                                <tr><td colspan="2" class="text-center text-muted">Belum ada data pengurus</td></tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card summary-card">
                    <div class="card-header card-header-brand-success">
                        <i class="fa fa-users mr-2"></i>Tabel Ringkas Pengguna
                    </div>
                    <div class="card-body p-3">
                        <div class="table-responsive">
                        <table class="table table-sm summary-table mb-0">
                            <thead><tr><th><i class="fa fa-at mr-1"></i>Username</th><th><i class="fa fa-user mr-1"></i>Nama</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($users,0,5) as $u): ?>
                                <tr><td><?= htmlspecialchars($u['username'] ?? '-') ?></td><td><?= htmlspecialchars($u['nama'] ?? $u['fname'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            <?php if(count($users) == 0): ?>
                                <tr><td colspan="2" class="text-center text-muted">Belum ada data pengguna</td></tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card summary-card">
                    <div class="card-header card-header-brand-warning">
                        <i class="fa fa-newspaper-o mr-2"></i>Tabel Ringkas Postingan
                    </div>
                    <div class="card-body p-3">
                        <div class="table-responsive">
                        <table class="table table-sm summary-table mb-0">
                            <thead><tr><th><i class="fa fa-file-text mr-1"></i>Judul</th><th><i class="fa fa-tag mr-1"></i>Kategori</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($posts,0,5) as $p): ?>
                                <tr><td><?= htmlspecialchars($p['judul'] ?? '-') ?></td><td><?= htmlspecialchars($p['kategori'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            <?php if(count($posts) == 0): ?>
                                <tr><td colspan="2" class="text-center text-muted">Belum ada data postingan</td></tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card summary-card">
                    <div class="card-header card-header-brand-info">
                        <i class="fa fa-tags mr-2"></i>Tabel Ringkas Kategori
                    </div>
                    <div class="card-body p-3">
                        <div class="table-responsive">
                        <table class="table table-sm summary-table mb-0">
                            <thead><tr><th><i class="fa fa-folder mr-1"></i>Nama Kategori</th><th><i class="fa fa-info-circle mr-1"></i>Deskripsi</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($categories,0,5) as $c): ?>
                                <tr><td><?= htmlspecialchars($c['nama'] ?? '-') ?></td><td><?= htmlspecialchars(substr($c['deskripsi'] ?? '-', 0, 30)) ?><?= strlen($c['deskripsi'] ?? '') > 30 ? '...' : '' ?></td></tr>
                            <?php endforeach; ?>
                            <?php if(count($categories) == 0): ?>
                                <tr><td colspan="2" class="text-center text-muted">Belum ada data kategori</td></tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 mb-4">
                <div class="card summary-card">
                    <div class="card-header card-header-brand-secondary">
                        <i class="fa fa-comments mr-2"></i>Tabel Ringkas Komentar Terbaru
                    </div>
                    <div class="card-body p-3">
                        <div class="table-responsive">
                        <table class="table table-sm summary-table mb-0">
                            <thead><tr><th><i class="fa fa-user mr-1"></i>Nama</th><th><i class="fa fa-comment mr-1"></i>Komentar</th><th><i class="fa fa-clock-o mr-1"></i>Tanggal</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($comments,0,8) as $c): ?>
                                <tr>
                                    <td><?= htmlspecialchars($c['nama'] ?? '-') ?></td>
                                    <td><?= htmlspecialchars(substr($c['isi'] ?? '-', 0, 50)) ?><?= strlen($c['isi'] ?? '') > 50 ? '...' : '' ?></td>
                                    <td><?= isset($c['tanggal']) ? date('d/m/Y', strtotime($c['tanggal'])) : '-' ?></td>
                                </tr>
                            <?php endforeach; ?>
                            <?php if(count($comments) == 0): ?>
                                <tr><td colspan="3" class="text-center text-muted">Belum ada komentar</td></tr>
                            <?php endif; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../js/jquery-1.12.4.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>

    <script>
    $(document).ready(function() {
        // Animate cards on load
        $('.admin-card').each(function(index) {
            $(this).delay(index * 100).animate({
                opacity: 1
            }, 500);
        });

        // Animate summary cards
        $('.summary-card').each(function(index) {
            $(this).delay((index + 6) * 100).animate({
                opacity: 1
            }, 500);
        });

        // Add hover effects to table rows
        $('.summary-table tbody tr').hover(
            function() {
                $(this).addClass('table-hover-effect');
            },
            function() {
                $(this).removeClass('table-hover-effect');
            }
        );

        // Add click effect to admin cards
        $('.admin-card').click(function() {
            var link = $(this).find('a').attr('href');
            if (link && !$(this).find('a').attr('onclick')) {
                window.location.href = link;
            }
        });

        // Add loading effect to buttons
        $('.btn').click(function() {
            var btn = $(this);
            var originalText = btn.html();

            if (!btn.attr('onclick') || !btn.attr('onclick').includes('confirm')) {
                btn.html('<i class="fa fa-spinner fa-spin mr-1"></i>Loading...');
                btn.prop('disabled', true);

                setTimeout(function() {
                    btn.html(originalText);
                    btn.prop('disabled', false);
                }, 1000);
            }
        });

        // Real-time clock
        function updateClock() {
            var now = new Date();
            var timeString = now.toLocaleTimeString('id-ID');
            var dateString = now.toLocaleDateString('id-ID', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });

            if ($('#clock').length === 0) {
                $('.welcome-header').after('<div id="clock" class="text-center mb-4" style="color: #666; font-size: 1.1rem;"><i class="fa fa-clock-o mr-2"></i>' + dateString + ' - ' + timeString + '</div>');
            } else {
                $('#clock').html('<i class="fa fa-clock-o mr-2"></i>' + dateString + ' - ' + timeString);
            }
        }

        updateClock();
        setInterval(updateClock, 1000);
    });
    </script>

    <style>
    .admin-card, .summary-card {
        opacity: 0;
    }

    .table-hover-effect {
        background: rgba(0,90,153,0.1) !important;
        transform: scale(1.01);
        transition: all 0.2s ease;
    }

    .admin-card {
        cursor: pointer;
    }

    #clock {
        animation: fadeIn 1s ease-in;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
    </style>
</body>
</html>
