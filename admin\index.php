<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Simple data fetch
try {
    $pengurus_count = 0;
    $users_count = 0;
    $posts_count = 0;
    $categories_count = 0;
    $comments_count = 0;
    
    // Count pengurus
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM pengurus");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $pengurus_count = $result['count'];
    } catch (Exception $e) {
        $pengurus_count = 0;
    }
    
    // Count users
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $users_count = $result['count'];
    } catch (Exception $e) {
        $users_count = 0;
    }
    
    // Count posts
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM posts");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $posts_count = $result['count'];
    } catch (Exception $e) {
        $posts_count = 0;
    }
    
    // Count categories
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM categories");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $categories_count = $result['count'];
    } catch (Exception $e) {
        $categories_count = 0;
    }
    
    // Count comments
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM comments");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $comments_count = $result['count'];
    } catch (Exception $e) {
        $comments_count = 0;
    }
    
} catch (Exception $e) {
    echo "Database error: " . $e->getMessage();
    exit();
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - UKM Panahan Gendewa Geni</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-family: 'Roboto', sans-serif;
        }
        .navbar {
            background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%) !important;
            box-shadow: 0 4px 20px rgba(0,90,153,0.2);
        }
        .admin-card {
            background: rgba(255,255,255,0.95);
            border: none;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,90,153,0.1);
            transition: all 0.3s ease;
        }
        .admin-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,90,153,0.2);
        }
        .admin-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: #fff;
            margin: 0 auto 15px;
        }
        .icon-primary { background: linear-gradient(135deg, #005a99, #0066cc); }
        .icon-success { background: linear-gradient(135deg, #28a745, #20c997); }
        .icon-warning { background: linear-gradient(135deg, #ff9800, #ffb74d); }
        .icon-info { background: linear-gradient(135deg, #17a2b8, #20c997); }
        .icon-secondary { background: linear-gradient(135deg, #6c757d, #868e96); }
        .btn-brand {
            border-radius: 20px;
            font-weight: 600;
            padding: 8px 20px;
            transition: all 0.3s ease;
        }
        .btn-brand:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>

<!-- Navbar -->
<nav class="navbar navbar-expand-lg navbar-dark">
    <div class="container-fluid">
        <a class="navbar-brand" href="index.php">
            <i class="fa fa-dashboard mr-2"></i>Admin Panel
        </a>
        <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav">
            <span class="navbar-toggler-icon"></span>
        </button>
        <div class="collapse navbar-collapse" id="navbarNav">
            <ul class="navbar-nav mr-auto">
                <li class="nav-item">
                    <a class="nav-link" href="index.php">Dashboard</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="pengurus.php">Pengurus</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="users.php">Users</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="post.php">Posts</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="category.php">Categories</a>
                </li>
            </ul>
            <ul class="navbar-nav">
                <li class="nav-item">
                    <span class="navbar-text mr-3">
                        <i class="fa fa-user"></i> <?= htmlspecialchars($_SESSION['username']) ?>
                    </span>
                </li>
                <li class="nav-item">
                    <a class="nav-link text-warning" href="../php/logout.php">
                        <i class="fa fa-sign-out"></i> Logout
                    </a>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Main Content -->
<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4" style="color: #005a99; font-weight: 700;">
                <i class="fa fa-dashboard mr-2"></i>Dashboard Admin
            </h2>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row">
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-primary">
                    <i class="fa fa-users"></i>
                </div>
                <h5>Pengurus</h5>
                <h3 class="text-primary"><?= $pengurus_count ?></h3>
                <a href="pengurus.php" class="btn btn-primary btn-brand btn-sm">Kelola</a>
            </div>
        </div>
        
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-success">
                    <i class="fa fa-user"></i>
                </div>
                <h5>Users</h5>
                <h3 class="text-success"><?= $users_count ?></h3>
                <a href="users.php" class="btn btn-success btn-brand btn-sm">Kelola</a>
            </div>
        </div>
        
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-warning">
                    <i class="fa fa-file-text"></i>
                </div>
                <h5>Posts</h5>
                <h3 class="text-warning"><?= $posts_count ?></h3>
                <a href="post.php" class="btn btn-warning btn-brand btn-sm">Kelola</a>
            </div>
        </div>
        
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-info">
                    <i class="fa fa-tags"></i>
                </div>
                <h5>Categories</h5>
                <h3 class="text-info"><?= $categories_count ?></h3>
                <a href="category.php" class="btn btn-info btn-brand btn-sm">Kelola</a>
            </div>
        </div>
        
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-secondary">
                    <i class="fa fa-comments"></i>
                </div>
                <h5>Comments</h5>
                <h3 class="text-secondary"><?= $comments_count ?></h3>
                <a href="comment.php" class="btn btn-secondary btn-brand btn-sm">Kelola</a>
            </div>
        </div>
        
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon" style="background: linear-gradient(135deg, #dc3545, #e74c3c);">
                    <i class="fa fa-home"></i>
                </div>
                <h5>Website</h5>
                <p class="mb-2">Kembali</p>
                <a href="../index.php" class="btn btn-danger btn-brand btn-sm">Lihat</a>
            </div>
        </div>
    </div>
    
    <!-- Quick Info -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card admin-card">
                <div class="card-header" style="background: linear-gradient(90deg, #005a99, #0066cc); color: #fff; border-radius: 15px 15px 0 0;">
                    <h5 class="mb-0"><i class="fa fa-info-circle mr-2"></i>Informasi Sistem</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>User Login:</strong> <?= htmlspecialchars($_SESSION['username']) ?></p>
                            <p><strong>Waktu Login:</strong> <?= date('d/m/Y H:i:s') ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Total Data:</strong> <?= $pengurus_count + $users_count + $posts_count + $categories_count + $comments_count ?></p>
                            <p><strong>Status:</strong> <span class="text-success">Online</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Add some interactivity
    $('.admin-card').hover(
        function() { $(this).addClass('shadow-lg'); },
        function() { $(this).removeClass('shadow-lg'); }
    );
});
</script>

</body>
</html>
