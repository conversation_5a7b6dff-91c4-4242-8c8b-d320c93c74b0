<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Real-time data fetch dengan error handling
try {
    $pengurus_count = 0;
    $users_count = 0;
    $posts_count = 0;
    $categories_count = 0;
    $comments_count = 0;

    // Count pengurus
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM pengurus");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $pengurus_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        $pengurus_count = 0;
        error_log("Error counting pengurus: " . $e->getMessage());
    }

    // Count users
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM users");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $users_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        $users_count = 0;
        error_log("Error counting users: " . $e->getMessage());
    }

    // Count posts - dengan nama tabel yang benar
    try {
        // Coba dengan nama tabel 'posts' dulu
        $stmt = $conn->query("SELECT COUNT(*) as count FROM posts");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $posts_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        try {
            // Fallback ke nama tabel 'post' jika 'posts' tidak ada
            $stmt = $conn->query("SELECT COUNT(*) as count FROM post");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $posts_count = $result['count'] ?? 0;
        } catch (Exception $e2) {
            $posts_count = 0;
            error_log("Error counting posts: " . $e2->getMessage());
        }
    }

    // Count categories - dengan nama tabel yang benar
    try {
        // Coba dengan nama tabel 'categories' dulu
        $stmt = $conn->query("SELECT COUNT(*) as count FROM categories");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $categories_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        try {
            // Fallback ke nama tabel 'category' jika 'categories' tidak ada
            $stmt = $conn->query("SELECT COUNT(*) as count FROM category");
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $categories_count = $result['count'] ?? 0;
        } catch (Exception $e2) {
            $categories_count = 0;
            error_log("Error counting categories: " . $e2->getMessage());
        }
    }

    // Count comments
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM comments");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $comments_count = $result['count'] ?? 0;
    } catch (Exception $e) {
        $comments_count = 0;
        error_log("Error counting comments: " . $e->getMessage());
    }

} catch (Exception $e) {
    error_log("Database error in admin dashboard: " . $e->getMessage());
    // Set default values jika ada error
    $pengurus_count = $users_count = $posts_count = $categories_count = $comments_count = 0;
}
?>

<!-- Dashboard Content -->
<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-dashboard mr-3"></i>Dashboard Admin - Selamat Datang <?= htmlspecialchars($_SESSION['username']) ?>
        </h1>
        <div class="btn-group">
            <button class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
                <i class="fa fa-refresh mr-1"></i>Refresh Data
            </button>
            <a href="../index.php" class="btn btn-outline-secondary btn-sm" target="_blank">
                <i class="fa fa-external-link mr-1"></i>Lihat Website
            </a>
        </div>
    </div>
    
    <!-- Stats Cards -->
    <div class="row mb-4">
        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-pengurus">
                    <i class="fa fa-user-tie"></i>
                </div>
                <div class="summary-title">Pengurus</div>
                <div class="mb-3">Jumlah: <b id="pengurus-count"><?= $pengurus_count ?></b></div>
                <a href="pengurus.php" class="btn btn-brand-primary btn-sm">
                    <i class="fa fa-cogs mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-users">
                    <i class="fa fa-users"></i>
                </div>
                <div class="summary-title">Pengguna</div>
                <div class="mb-3">Jumlah: <b id="users-count"><?= $users_count ?></b></div>
                <a href="users.php" class="btn btn-brand-success btn-sm">
                    <i class="fa fa-user-plus mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-posts">
                    <i class="fa fa-newspaper-o"></i>
                </div>
                <div class="summary-title">Postingan</div>
                <div class="mb-3">Jumlah: <b id="posts-count"><?= $posts_count ?></b></div>
                <a href="post.php" class="btn btn-brand-warning btn-sm">
                    <i class="fa fa-edit mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-categories">
                    <i class="fa fa-tags"></i>
                </div>
                <div class="summary-title">Kategori</div>
                <div class="mb-3">Jumlah: <b id="categories-count"><?= $categories_count ?></b></div>
                <a href="category.php" class="btn btn-brand-info btn-sm">
                    <i class="fa fa-folder-open mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-comments">
                    <i class="fa fa-comments"></i>
                </div>
                <div class="summary-title">Komentar</div>
                <div class="mb-3">Jumlah: <b id="comments-count"><?= $comments_count ?></b></div>
                <a href="comment.php" class="btn btn-secondary btn-sm" style="background: linear-gradient(45deg, #6c757d, #868e96); border: none; color: #fff; border-radius: 25px; font-weight: 600;">
                    <i class="fa fa-comment mr-1"></i>Kelola
                </a>
            </div>
        </div>

        <div class="col-md-2 col-6 mb-3">
            <div class="card admin-card text-center p-3">
                <div class="admin-icon icon-logout">
                    <i class="fa fa-home"></i>
                </div>
                <div class="summary-title">Website</div>
                <div class="mb-3">Kembali</div>
                <a href="../index.php" class="btn btn-brand-danger btn-sm">
                    <i class="fa fa-external-link mr-1"></i>Lihat
                </a>
            </div>
        </div>
    </div>

    <!-- Quick Info -->
    <div class="row">
        <div class="col-12">
            <div class="card summary-card">
                <div class="card-header card-header-brand-primary">
                    <i class="fa fa-info-circle mr-2"></i>Informasi Sistem
                </div>
                <div class="card-body p-4">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong><i class="fa fa-user mr-2" style="color: #005a99;"></i>User Login:</strong> <?= htmlspecialchars($_SESSION['username']) ?></p>
                            <p><strong><i class="fa fa-clock-o mr-2" style="color: #ff9800;"></i>Waktu Akses:</strong> <span id="current-time"><?= date('d/m/Y H:i:s') ?></span></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong><i class="fa fa-database mr-2" style="color: #17a2b8;"></i>Total Data:</strong> <span id="total-data"><?= $pengurus_count + $users_count + $posts_count + $categories_count + $comments_count ?></span></p>
                            <p><strong><i class="fa fa-signal mr-2" style="color: #28a745;"></i>Status:</strong> <span class="text-success">Online & Aktif</span></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Animate cards on load
    $('.admin-card').each(function(index) {
        $(this).delay(index * 100).animate({
            opacity: 1
        }, 500);
    });

    // Add hover effects
    $('.admin-card').hover(
        function() {
            $(this).addClass('shadow-lg');
        },
        function() {
            $(this).removeClass('shadow-lg');
        }
    );

    // Real-time clock
    function updateClock() {
        var now = new Date();
        var timeString = now.toLocaleTimeString('id-ID');
        var dateString = now.toLocaleDateString('id-ID', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        $('#current-time').text(dateString + ' - ' + timeString);
    }

    updateClock();
    setInterval(updateClock, 1000);

    // Auto refresh data every 30 seconds
    setInterval(refreshStats, 30000);
});

// Function to refresh statistics
function refreshStats() {
    $.ajax({
        url: 'ajax/get-stats.php',
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            if (data.success) {
                $('#pengurus-count').text(data.pengurus_count);
                $('#users-count').text(data.users_count);
                $('#posts-count').text(data.posts_count);
                $('#categories-count').text(data.categories_count);
                $('#comments-count').text(data.comments_count);
                $('#total-data').text(data.total_count);

                // Add animation effect
                $('.admin-card').addClass('pulse');
                setTimeout(function() {
                    $('.admin-card').removeClass('pulse');
                }, 1000);
            }
        },
        error: function() {
            console.log('Error refreshing stats');
        }
    });
}
</script>

<style>
.admin-card {
    opacity: 0;
}

.pulse {
    animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.navbar-nav .nav-link:hover {
    background: rgba(255,255,255,0.2);
    border-radius: 8px;
    transform: translateY(-1px);
}

.dropdown-menu {
    border: none;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    border-radius: 10px;
}

.dropdown-item:hover {
    background: rgba(0,90,153,0.1);
    color: #005a99;
}

#clock {
    animation: fadeIn 1s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
</style>

</body>
</html>
