<?php
include __DIR__ . '/inc/header.php';
$key = "hhdsfs1263z";
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}
// Load data handler
include_once __DIR__ . '/data/pengurus.php';
include_once __DIR__ . '/data/user.php';
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';
include_once __DIR__ . '/data/comment.php';
// DB connection
include_once __DIR__ . '/../db_conn.php';

// Ambil data
$pengurus = getAllPengurus($conn);
$users = getAllUsers($conn);
$posts = getAllPosts($conn);
$categories = getAllCategories($conn);
$comments = getAllComments($conn);

// Ambil data admin
$admin = null;
if (isset($_SESSION['username'])) {
    $stmt = $conn->prepare("SELECT * FROM admin WHERE username = ? LIMIT 1");
    $stmt->execute([$_SESSION['username']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <style>
        body { background: #f8f9fa; }
        .dashboard-content {
            margin: 40px auto;
            padding: 40px 24px;
            max-width: 1400px;
            min-height: 80vh;
        }
        .admin-card {
            border: none;
            border-radius: 18px;
            transition: box-shadow 0.2s, transform 0.2s;
            background: #fff;
            box-shadow: 0 2px 12px rgba(0,0,0,0.07);
        }
        .admin-card:hover {
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            transform: translateY(-4px) scale(1.04);
        }
        .admin-icon {
            width: 70px;
            height: 70px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff 60%, #6c63ff 100%);
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 2.5rem;
            margin: 0 auto 18px auto;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }
        .admin-link {
            margin-top: 12px;
        }
        .summary-table th, .summary-table td {
            font-size: 0.97rem;
            vertical-align: middle;
        }
        .summary-title {
            font-size: 1.1rem;
            font-weight: bold;
            margin-bottom: 2px;
        }
        .card-header {
            border-radius: 18px 18px 0 0 !important;
        }
        .card {
            border-radius: 18px !important;
        }
        .table {
            background: #fff;
        }
        @media (max-width: 991px) {
            .dashboard-content { margin: 24px 4px; padding: 16px 2px; }
        }
        @media (max-width: 600px) {
            .dashboard-content { margin: 8px 0; padding: 8px 0; }
        }
    </style>
</head>
<body>
    <div class="dashboard-content">
        <h1 class="mb-4 text-primary font-weight-bold">
            Selamat Datang <?= isset($_SESSION['username']) ? htmlspecialchars($_SESSION['username']) : 'Admin' ?>
        </h1>
        <div class="row mb-4">
            <div class="col-md-2 col-6 mb-2">
                <div class="card admin-card shadow-sm text-center">
                    <div class="admin-icon bg-primary"><i class="fa fa-user"></i></div>
                    <div class="summary-title">Pengurus</div>
                    <div class="mb-2">Jumlah: <b><?= count($pengurus) ?></b></div>
                    <a href="pengurus.php" class="btn btn-outline-primary btn-sm admin-link">Kelola</a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-2">
                <div class="card admin-card shadow-sm text-center">
                    <div class="admin-icon bg-success"><i class="fa fa-users"></i></div>
                    <div class="summary-title">Pengguna</div>
                    <div class="mb-2">Jumlah: <b><?= count($users) ?></b></div>
                    <a href="users.php" class="btn btn-outline-success btn-sm admin-link">Kelola</a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-2">
                <div class="card admin-card shadow-sm text-center">
                    <div class="admin-icon bg-warning"><i class="fa fa-wpforms"></i></div>
                    <div class="summary-title">Postingan</div>
                    <div class="mb-2">Jumlah: <b><?= count($posts) ?></b></div>
                    <a href="post.php" class="btn btn-outline-warning btn-sm admin-link">Kelola</a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-2">
                <div class="card admin-card shadow-sm text-center">
                    <div class="admin-icon bg-info"><i class="fa fa-window-restore"></i></div>
                    <div class="summary-title">Kategori</div>
                    <div class="mb-2">Jumlah: <b><?= count($categories) ?></b></div>
                    <a href="category.php" class="btn btn-outline-info btn-sm admin-link">Kelola</a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-2">
                <div class="card admin-card shadow-sm text-center">
                    <div class="admin-icon bg-secondary"><i class="fa fa-comment-o"></i></div>
                    <div class="summary-title">Komentar</div>
                    <div class="mb-2">Jumlah: <b><?= count($comments) ?></b></div>
                    <a href="comment.php" class="btn btn-outline-secondary btn-sm admin-link">Kelola</a>
                </div>
            </div>
            <div class="col-md-2 col-6 mb-2">
                <div class="card admin-card shadow-sm text-center">
                    <div class="admin-icon bg-danger"><i class="fa fa-power-off"></i></div>
                    <div class="summary-title">Logout</div>
                    <div class="mb-2">&nbsp;</div>
                    <a href="../php/logout.php" class="btn btn-outline-danger btn-sm admin-link">Logout</a>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">Tabel Ringkas Pengurus</div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                        <table class="table table-sm table-bordered summary-table mb-0">
                            <thead><tr><th>Nama</th><th>Jabatan</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($pengurus,0,5) as $p): ?>
                                <tr><td><?= htmlspecialchars($p['nama'] ?? '-') ?></td><td><?= htmlspecialchars($p['jabatan'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-success text-white">Tabel Ringkas Pengguna</div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                        <table class="table table-sm table-bordered summary-table mb-0">
                            <thead><tr><th>Username</th><th>Nama</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($users,0,5) as $u): ?>
                                <tr><td><?= htmlspecialchars($u['username'] ?? '-') ?></td><td><?= htmlspecialchars($u['nama'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-warning text-white">Tabel Ringkas Postingan</div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                        <table class="table table-sm table-bordered summary-table mb-0">
                            <thead><tr><th>Judul</th><th>Kategori</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($posts,0,5) as $p): ?>
                                <tr><td><?= htmlspecialchars($p['judul'] ?? '-') ?></td><td><?= htmlspecialchars($p['kategori'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-info text-white">Tabel Ringkas Kategori</div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                        <table class="table table-sm table-bordered summary-table mb-0">
                            <thead><tr><th>Nama Kategori</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($categories,0,5) as $c): ?>
                                <tr><td><?= htmlspecialchars($c['nama'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-secondary text-white">Tabel Ringkas Komentar</div>
                    <div class="card-body p-2">
                        <div class="table-responsive">
                        <table class="table table-sm table-bordered summary-table mb-0">
                            <thead><tr><th>Nama</th><th>Komentar</th></tr></thead>
                            <tbody>
                            <?php foreach(array_slice($comments,0,5) as $c): ?>
                                <tr><td><?= htmlspecialchars($c['nama'] ?? '-') ?></td><td><?= htmlspecialchars($c['isi'] ?? '-') ?></td></tr>
                            <?php endforeach; ?>
                            </tbody>
                        </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <script src="../js/jquery-1.12.4.min.js"></script>
    <script src="../js/bootstrap.min.js"></script>
</body>
</html>
