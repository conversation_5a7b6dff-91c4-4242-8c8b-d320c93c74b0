<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handlers
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: post.php?error=' . urlencode('ID postingan tidak valid'));
    exit();
}

$id = intval($_GET['id']);
$errors = [];

// Get post data
try {
    $post = function_exists('getPostById') ? getPostById($conn, $id) : null;
    if (!$post) {
        // Fallback to direct query - prioritas tabel 'post' dulu
        $stmt = $conn->prepare("SELECT * FROM post WHERE id = ?");
        $stmt->execute([$id]);
        $post = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$post) {
            // Try with 'posts' table name sebagai fallback
            $stmt = $conn->prepare("SELECT * FROM posts WHERE id = ?");
            $stmt->execute([$id]);
            $post = $stmt->fetch(PDO::FETCH_ASSOC);
        }
    }

    if (!$post) {
        header('Location: post.php?error=' . urlencode('Postingan tidak ditemukan'));
        exit();
    }
} catch (Exception $e) {
    header('Location: post.php?error=' . urlencode('Error: ' . $e->getMessage()));
    exit();
}

// Get categories
try {
    $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
} catch (Exception $e) {
    $categories = [];
    $errors[] = 'Error mengambil data kategori: ' . $e->getMessage();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $content = isset($_POST['content']) ? trim($_POST['content']) : '';
    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
    $cover_url = $post['cover_url'];
    // Upload cover baru jika ada
    $uploadDir = realpath(__DIR__ . '/../upload/blog');
    if ($uploadDir === false) {
        $baseUpload = __DIR__ . '/../upload/blog';
        if (!is_dir($baseUpload)) {
            mkdir($baseUpload, 0777, true);
        }
        $uploadDir = realpath($baseUpload);
    }
    if (isset($_FILES['cover']) && $_FILES['cover']['error'] === UPLOAD_ERR_OK) {
        $ext = pathinfo($_FILES['cover']['name'], PATHINFO_EXTENSION);
        $filename = 'post_' . time() . '.' . $ext;
        $target = $uploadDir . DIRECTORY_SEPARATOR . $filename;
        if (move_uploaded_file($_FILES['cover']['tmp_name'], $target)) {
            $cover_url = $filename;
        }
    }
    // Update ke database
    $stmt = $conn->prepare("UPDATE post SET post_title=?, post_text=?, category_id=?, cover_url=? WHERE id=?");
    $stmt->execute([$title, $content, $category_id, $cover_url, $id]);
    header('Location: post.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Edit Postingan</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Edit Postingan</h2>
    <form method="post" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="title" class="form-label">Judul</label>
            <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($post['post_title']) ?>" required>
        </div>
        <div class="mb-3">
            <label for="content" class="form-label">Konten</label>
            <textarea class="form-control" id="content" name="content" rows="5" required><?= htmlspecialchars($post['post_text']) ?></textarea>
        </div>
        <div class="mb-3">
            <label for="category_id" class="form-label">Kategori</label>
            <select class="form-control" id="category_id" name="category_id" required>
                <option value="">Pilih Kategori</option>
                <?php foreach ($categories as $cat): ?>
                    <option value="<?= intval($cat['id']) ?>" <?= $cat['id'] == $post['category_id'] ? 'selected' : '' ?>><?= htmlspecialchars($cat['category']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label for="cover" class="form-label">Cover (opsional)</label>
            <?php if (!empty($post['cover_url'])): ?>
                <div class="mb-2"><img src="../upload/blog/<?= htmlspecialchars($post['cover_url']) ?>" alt="cover" width="100"></div>
            <?php endif; ?>
            <input type="file" class="form-control" id="cover" name="cover" accept="image/*">
        </div>
        <button type="submit" class="btn btn-success">Update</button>
        <a href="post.php" class="btn btn-secondary">Kembali</a>
    </form>
</div>
</body>
</html>
