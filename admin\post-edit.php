<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
// TODO: Proses edit postingan
include __DIR__ . '/inc/header.php';
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$conn = $conn ?? (include __DIR__ . '/../db_conn.php');
$post = ($id > 0 && function_exists('getPostById')) ? getPostById($conn, $id) : null;
$categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];

if (!$post) {
    echo '<div class="container mt-5"><div class="alert alert-danger">Data postingan tidak ditemukan.</div></div>';
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $content = isset($_POST['content']) ? trim($_POST['content']) : '';
    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
    $cover_url = $post['cover_url'];
    // Upload cover baru jika ada
    $uploadDir = realpath(__DIR__ . '/../upload/blog');
    if ($uploadDir === false) {
        $baseUpload = __DIR__ . '/../upload/blog';
        if (!is_dir($baseUpload)) {
            mkdir($baseUpload, 0777, true);
        }
        $uploadDir = realpath($baseUpload);
    }
    if (isset($_FILES['cover']) && $_FILES['cover']['error'] === UPLOAD_ERR_OK) {
        $ext = pathinfo($_FILES['cover']['name'], PATHINFO_EXTENSION);
        $filename = 'post_' . time() . '.' . $ext;
        $target = $uploadDir . DIRECTORY_SEPARATOR . $filename;
        if (move_uploaded_file($_FILES['cover']['tmp_name'], $target)) {
            $cover_url = $filename;
        }
    }
    // Update ke database
    $stmt = $conn->prepare("UPDATE post SET post_title=?, post_text=?, category_id=?, cover_url=? WHERE id=?");
    $stmt->execute([$title, $content, $category_id, $cover_url, $id]);
    header('Location: post.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Edit Postingan</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Edit Postingan</h2>
    <form method="post" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="title" class="form-label">Judul</label>
            <input type="text" class="form-control" id="title" name="title" value="<?= htmlspecialchars($post['post_title']) ?>" required>
        </div>
        <div class="mb-3">
            <label for="content" class="form-label">Konten</label>
            <textarea class="form-control" id="content" name="content" rows="5" required><?= htmlspecialchars($post['post_text']) ?></textarea>
        </div>
        <div class="mb-3">
            <label for="category_id" class="form-label">Kategori</label>
            <select class="form-control" id="category_id" name="category_id" required>
                <option value="">Pilih Kategori</option>
                <?php foreach ($categories as $cat): ?>
                    <option value="<?= intval($cat['id']) ?>" <?= $cat['id'] == $post['category_id'] ? 'selected' : '' ?>><?= htmlspecialchars($cat['category']) ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        <div class="mb-3">
            <label for="cover" class="form-label">Cover (opsional)</label>
            <?php if (!empty($post['cover_url'])): ?>
                <div class="mb-2"><img src="../upload/blog/<?= htmlspecialchars($post['cover_url']) ?>" alt="cover" width="100"></div>
            <?php endif; ?>
            <input type="file" class="form-control" id="cover" name="cover" accept="image/*">
        </div>
        <button type="submit" class="btn btn-success">Update</button>
        <a href="post.php" class="btn btn-secondary">Kembali</a>
    </form>
</div>
</body>
</html>
