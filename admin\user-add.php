<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $username = trim($_POST['username'] ?? '');
    $fname = trim($_POST['fname'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $password = $_POST['password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    $role = $_POST['role'] ?? 'anggota';

    // Validation
    if (empty($username)) {
        $errors[] = 'Username tidak boleh kosong';
    } elseif (strlen($username) < 3) {
        $errors[] = 'Username minimal 3 karakter';
    }

    if (empty($fname)) {
        $errors[] = 'Nama lengkap tidak boleh kosong';
    }

    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Email tidak valid';
    }

    if (empty($password)) {
        $errors[] = 'Password tidak boleh kosong';
    } elseif (strlen($password) < 6) {
        $errors[] = 'Password minimal 6 karakter';
    }

    if ($password !== $confirm_password) {
        $errors[] = 'Konfirmasi password tidak cocok';
    }

    // Check if username or email already exists
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
            $stmt->execute([$username, $email]);
            if ($stmt->fetch()) {
                $errors[] = 'Username atau email sudah digunakan';
            }
        } catch (Exception $e) {
            $errors[] = 'Error checking existing user: ' . $e->getMessage();
        }
    }

    // Insert new user
    if (empty($errors)) {
        try {
            $hashed_password = password_hash($password, PASSWORD_DEFAULT);
            $stmt = $conn->prepare("INSERT INTO users (username, fname, email, password, role, created_at) VALUES (?, ?, ?, ?, ?, NOW())");
            $result = $stmt->execute([$username, $fname, $email, $hashed_password, $role]);

            if ($result) {
                header('Location: users.php?success=' . urlencode('Pengguna berhasil ditambahkan'));
                exit();
            } else {
                $errors[] = 'Gagal menambahkan pengguna';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-user-plus mr-3"></i>Tambah Pengguna Baru
        </h1>
        <a href="users.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-success">
            <i class="fa fa-user-plus mr-2"></i>Form Tambah Pengguna
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="username"><i class="fa fa-user mr-1"></i>Username *</label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="<?= htmlspecialchars($_POST['username'] ?? '') ?>"
                                   placeholder="Masukkan username" required>
                            <small class="form-text text-muted">Minimal 3 karakter, hanya huruf, angka, dan underscore</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="fname"><i class="fa fa-id-card mr-1"></i>Nama Lengkap *</label>
                            <input type="text" class="form-control" id="fname" name="fname"
                                   value="<?= htmlspecialchars($_POST['fname'] ?? '') ?>"
                                   placeholder="Masukkan nama lengkap" required>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email"><i class="fa fa-envelope mr-1"></i>Email *</label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="<?= htmlspecialchars($_POST['email'] ?? '') ?>"
                                   placeholder="Masukkan email" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="role"><i class="fa fa-shield mr-1"></i>Role</label>
                            <select class="form-control" id="role" name="role">
                                <option value="anggota" <?= ($_POST['role'] ?? '') == 'anggota' ? 'selected' : '' ?>>Anggota</option>
                                <option value="admin" <?= ($_POST['role'] ?? '') == 'admin' ? 'selected' : '' ?>>Admin</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="password"><i class="fa fa-lock mr-1"></i>Password *</label>
                            <input type="password" class="form-control" id="password" name="password"
                                   placeholder="Masukkan password" required>
                            <small class="form-text text-muted">Minimal 6 karakter</small>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="confirm_password"><i class="fa fa-lock mr-1"></i>Konfirmasi Password *</label>
                            <input type="password" class="form-control" id="confirm_password" name="confirm_password"
                                   placeholder="Ulangi password" required>
                        </div>
                    </div>
                </div>

                <div class="form-group text-right">
                    <a href="users.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-success">
                        <i class="fa fa-save mr-1"></i>Simpan Pengguna
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Username validation
    $('#username').on('input', function() {
        var username = $(this).val();
        var regex = /^[a-zA-Z0-9_]+$/;

        if (username.length > 0 && !regex.test(username)) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Username hanya boleh mengandung huruf, angka, dan underscore</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Password confirmation
    $('#confirm_password').on('input', function() {
        var password = $('#password').val();
        var confirm = $(this).val();

        if (confirm.length > 0 && password !== confirm) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Password tidak cocok</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;

        // Check required fields
        $(this).find('input[required]').each(function() {
            if (!$(this).val().trim()) {
                $(this).addClass('is-invalid');
                isValid = false;
            } else {
                $(this).removeClass('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Mohon lengkapi semua field yang wajib diisi!');
        }
    });
});
</script>

</body>
</html>
