<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}
include_once __DIR__ . '/../../db_conn.php';
// Ambil data admin dari database
$admin = null;
if (isset($_SESSION['username'])) {
    $stmt = $conn->prepare("SELECT * FROM admin WHERE username = ? LIMIT 1");
    $stmt->execute([$_SESSION['username']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);
}
?>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary shadow-sm" style="min-height:64px;">
  <div class="container-fluid justify-content-center">
    <a class="navbar-brand fw-bold px-3" href="/admin/index.php" style="font-size:1.4rem;letter-spacing:1px;">Admin Panel</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse justify-content-center" id="navbarNav">
      <ul class="navbar-nav gap-2">
        <li class="nav-item">
          <a class="nav-link" href="/admin/index.php">Dashboard</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/pengurus.php">Pengurus</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/users.php">Pengguna</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/post.php">Postingan</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/category.php">Kategori</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/comment.php">Komentar</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="/admin/galeri.php">Galeri</a>
        <?php if ($admin): ?>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fa fa-user"></i> <?= htmlspecialchars($admin['username']) ?>
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
            <li><a class="dropdown-item" href="#">Profil</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="/php/logout.php">Logout</a></li>
          </ul>
        </li>
        <?php endif; ?>
      </ul>
    </div>
  </div>
</nav>
<style>
.navbar {
  border-radius: 0 0 18px 18px;
  box-shadow: 0 2px 12px rgba(0,0,0,0.07);
}
.navbar-nav .nav-link {
  font-size: 1.08rem;
  font-weight: 500;
  padding: 8px 18px;
  border-radius: 8px;
  transition: background 0.2s, color 0.2s;
}
.navbar-nav .nav-link.active, .navbar-nav .nav-link:hover {
  background: #fff;
  color: #007bff !important;
}
.navbar-brand {
  font-size: 1.4rem;
  letter-spacing: 1px;
}
@media (max-width: 991px) {
  .navbar-nav { justify-content: center !important; }
  .navbar-brand { margin-bottom: 8px; }
}
</style>
<link rel="stylesheet" href="/css/bootstrap.min.css">
<link rel="stylesheet" href="/css/font-awesome.min.css">
<script src="/js/bootstrap.min.js"></script>
