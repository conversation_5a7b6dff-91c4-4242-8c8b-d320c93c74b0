<?php
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

include_once __DIR__ . '/../../db_conn.php';

// Ambil data admin dari database
$admin = null;
if (isset($_SESSION['username'])) {
    $stmt = $conn->prepare("SELECT * FROM admin WHERE username = ? LIMIT 1");
    $stmt->execute([$_SESSION['username']]);
    $admin = $stmt->fetch(PDO::FETCH_ASSOC);

    // Jika tidak ada di tabel admin, cek di tabel users dengan role admin
    if (!$admin) {
        $stmt = $conn->prepare("SELECT * FROM users WHERE username = ? AND (role = 'admin' OR role = 'Admin') LIMIT 1");
        $stmt->execute([$_SESSION['username']]);
        $admin = $stmt->fetch(PDO::FETCH_ASSOC);
    }
}
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Panel - UKM Panahan Gendewa Geni</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
    <link rel="stylesheet" href="../css/font-awesome.min.css">
    <link rel="stylesheet" href="../css/style.css">
    <link rel="stylesheet" href="css/admin-custom.css">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&family=Teko:wght@300..700&display=swap" rel="stylesheet">
</head>
<body>

<nav class="navbar navbar-expand-lg navbar-dark shadow-sm" style="background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%); min-height:64px;">
  <div class="container-fluid">
    <a class="navbar-brand fw-bold px-3" href="index.php" style="font-size:1.4rem;letter-spacing:1px;">
        <i class="fa fa-dashboard mr-2"></i>Admin Panel
    </a>
    <button class="navbar-toggler" type="button" data-toggle="collapse" data-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav me-auto">
        <li class="nav-item">
          <a class="nav-link" href="index.php"><i class="fa fa-dashboard mr-1"></i>Dashboard</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="pengurus.php"><i class="fa fa-user-tie mr-1"></i>Pengurus</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="users.php"><i class="fa fa-users mr-1"></i>Pengguna</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="post.php"><i class="fa fa-newspaper-o mr-1"></i>Postingan</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="category.php"><i class="fa fa-tags mr-1"></i>Kategori</a>
        </li>
        <li class="nav-item">
          <a class="nav-link" href="comment.php"><i class="fa fa-comments mr-1"></i>Komentar</a>
        </li>
      </ul>
      <ul class="navbar-nav">
        <?php if ($admin): ?>
        <li class="nav-item dropdown">
          <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-toggle="dropdown" aria-expanded="false">
            <i class="fa fa-user-circle mr-1"></i> <?= htmlspecialchars($admin['username'] ?? $_SESSION['username']) ?>
          </a>
          <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
            <li><a class="dropdown-item" href="../profil.php"><i class="fa fa-user mr-2"></i>Profil</a></li>
            <li><a class="dropdown-item" href="../index.php"><i class="fa fa-home mr-2"></i>Ke Website</a></li>
            <li><hr class="dropdown-divider"></li>
            <li><a class="dropdown-item text-danger" href="../php/logout.php"><i class="fa fa-sign-out mr-2"></i>Logout</a></li>
          </ul>
        </li>
        <?php else: ?>
        <li class="nav-item">
          <a class="nav-link text-warning" href="../login.php"><i class="fa fa-sign-in mr-1"></i>Login</a>
        </li>
        <?php endif; ?>
      </ul>
    </div>
  </div>
</nav>
<style>
.navbar {
  border-radius: 0 0 18px 18px;
  box-shadow: 0 4px 20px rgba(0,90,153,0.2);
}
.navbar-nav .nav-link {
  font-size: 1rem;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 8px;
  transition: all 0.3s ease;
  color: rgba(255,255,255,0.9) !important;
}
.navbar-nav .nav-link:hover {
  background: rgba(255,255,255,0.2);
  color: #fff !important;
  transform: translateY(-1px);
}
.navbar-nav .nav-link.active {
  background: rgba(255,255,255,0.3);
  color: #fff !important;
}
.navbar-brand {
  font-size: 1.4rem;
  letter-spacing: 1px;
  color: #fff !important;
}
.dropdown-menu {
  border: none;
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
  border-radius: 10px;
}
.dropdown-item {
  padding: 8px 16px;
  transition: all 0.2s ease;
}
.dropdown-item:hover {
  background: rgba(0,90,153,0.1);
  color: #005a99;
}
@media (max-width: 991px) {
  .navbar-nav { justify-content: center !important; }
  .navbar-brand { margin-bottom: 8px; }
  .navbar-nav .nav-link { margin: 2px 0; }
}
</style>
