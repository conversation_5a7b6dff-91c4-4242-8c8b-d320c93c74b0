<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: comment.php?error=' . urlencode('ID komentar tidak valid'));
    exit();
}

$comment_id = intval($_GET['id']);
$comment = null;
$post_title = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $nama = trim($_POST['nama'] ?? '');
    $email = trim($_POST['email'] ?? '');
    $isi = trim($_POST['isi'] ?? '');
    $status = $_POST['status'] ?? 'pending';
    
    $errors = [];
    
    // Validation
    if (empty($nama)) {
        $errors[] = 'Nama tidak boleh kosong';
    }
    
    if (empty($email) || !filter_var($email, FILTER_VALIDATE_EMAIL)) {
        $errors[] = 'Email tidak valid';
    }
    
    if (empty($isi)) {
        $errors[] = 'Isi komentar tidak boleh kosong';
    }
    
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("UPDATE comments SET nama = ?, email = ?, isi = ?, status = ? WHERE id = ?");
            $result = $stmt->execute([$nama, $email, $isi, $status, $comment_id]);
            
            if ($result) {
                header('Location: comment.php?success=' . urlencode('Komentar berhasil diupdate'));
                exit();
            } else {
                $errors[] = 'Gagal mengupdate komentar';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}

// Get comment data
try {
    $stmt = $conn->prepare("SELECT * FROM comments WHERE id = ?");
    $stmt->execute([$comment_id]);
    $comment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$comment) {
        header('Location: comment.php?error=' . urlencode('Komentar tidak ditemukan'));
        exit();
    }
    
    // Get post title
    $stmt = $conn->prepare("SELECT post_title FROM posts WHERE id = ?");
    $stmt->execute([$comment['post_id']]);
    $post_title = $stmt->fetchColumn() ?: 'Post tidak ditemukan';
    
} catch (Exception $e) {
    header('Location: comment.php?error=' . urlencode('Error: ' . $e->getMessage()));
    exit();
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-edit mr-3"></i>Edit Komentar
        </h1>
        <a href="comment.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-secondary">
            <i class="fa fa-edit mr-2"></i>Form Edit Komentar
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="nama"><i class="fa fa-user mr-1"></i>Nama Komentator</label>
                            <input type="text" class="form-control" id="nama" name="nama" 
                                   value="<?= htmlspecialchars($comment['nama'] ?? '') ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label for="email"><i class="fa fa-envelope mr-1"></i>Email</label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="<?= htmlspecialchars($comment['email'] ?? '') ?>" required>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="post_title"><i class="fa fa-newspaper-o mr-1"></i>Post Terkait</label>
                    <input type="text" class="form-control" value="<?= htmlspecialchars($post_title) ?>" readonly>
                </div>
                
                <div class="form-group">
                    <label for="isi"><i class="fa fa-comment mr-1"></i>Isi Komentar</label>
                    <textarea class="form-control" id="isi" name="isi" rows="5" required><?= htmlspecialchars($comment['isi'] ?? '') ?></textarea>
                </div>
                
                <div class="form-group">
                    <label for="status"><i class="fa fa-check-circle mr-1"></i>Status</label>
                    <select class="form-control" id="status" name="status">
                        <option value="pending" <?= ($comment['status'] ?? '') == 'pending' ? 'selected' : '' ?>>
                            <i class="fa fa-clock-o"></i> Pending
                        </option>
                        <option value="approved" <?= ($comment['status'] ?? '') == 'approved' ? 'selected' : '' ?>>
                            <i class="fa fa-check"></i> Disetujui
                        </option>
                        <option value="rejected" <?= ($comment['status'] ?? '') == 'rejected' ? 'selected' : '' ?>>
                            <i class="fa fa-times"></i> Ditolak
                        </option>
                    </select>
                </div>
                
                <div class="form-group">
                    <label><i class="fa fa-info-circle mr-1"></i>Informasi Tambahan</label>
                    <div class="row">
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>Tanggal Komentar:</strong><br>
                                <?= isset($comment['tanggal']) ? date('d/m/Y H:i:s', strtotime($comment['tanggal'])) : '-' ?>
                            </small>
                        </div>
                        <div class="col-md-6">
                            <small class="text-muted">
                                <strong>IP Address:</strong><br>
                                <?= htmlspecialchars($comment['ip_address'] ?? 'Tidak tersedia') ?>
                            </small>
                        </div>
                    </div>
                </div>
                
                <div class="form-group text-right">
                    <a href="comment.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-secondary">
                        <i class="fa fa-save mr-1"></i>Update Komentar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto-resize textarea
    $('textarea').on('input', function() {
        this.style.height = 'auto';
        this.style.height = (this.scrollHeight) + 'px';
    });
    
    // Form validation
    $('form').on('submit', function(e) {
        var nama = $('#nama').val().trim();
        var email = $('#email').val().trim();
        var isi = $('#isi').val().trim();
        
        if (!nama || !email || !isi) {
            e.preventDefault();
            alert('Semua field harus diisi!');
            return false;
        }
        
        // Email validation
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
            e.preventDefault();
            alert('Format email tidak valid!');
            return false;
        }
    });
});
</script>

</body>
</html>
