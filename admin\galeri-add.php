<?php
$headerPath = __DIR__ . '/inc/header.php';
if (file_exists($headerPath)) {
    include_once $headerPath;
}
include_once __DIR__ . "/data/galeri.php";
include_once __DIR__ . "/../db_conn.php";
$msg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $judul = $_POST['judul'];
    $deskripsi = $_POST['deskripsi'];
    $gambar = '';
    if (!empty($_FILES['gambar']['name'])) {
        $ext = pathinfo($_FILES['gambar']['name'], PATHINFO_EXTENSION);
        $namaFile = time().'_'.rand(100,999).'.'.$ext;
        $tujuan = __DIR__ . "/../upload/galeri/".$namaFile;
        if (move_uploaded_file($_FILES['gambar']['tmp_name'], $tujuan)) {
            $gambar = $namaFile;
        }
    }
    if (addGaleri($conn, $judul, $gambar, $deskripsi)) {
        header("Location: galeri.php"); exit;
    } else {
        $msg = 'Gagal menambah galeri!';
    }
}
?>
<div class="container mt-4">
    <div class="card shadow-sm mx-auto" style="max-width:520px;">
        <div class="card-header bg-primary text-white" style="background:linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important;">
            <h4 class="mb-0"><i class="fa fa-plus mr-2"></i>Tambah Galeri</h4>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label>Judul</label>
                    <input type="text" name="judul" class="form-control" required>
                </div>
                <div class="form-group">
                    <label>Deskripsi</label>
                    <textarea name="deskripsi" class="form-control" required></textarea>
                </div>
                <div class="form-group">
                    <label>Gambar/Video</label>
                    <input type="file" name="gambar" class="form-control-file" accept="image/*,video/*" required>
                </div>
                <button type="submit" class="btn btn-success">Simpan</button>
                <a href="galeri.php" class="btn btn-secondary">Kembali</a>
                <div class="text-danger mt-2"><?=$msg?></div>
            </form>
        </div>
    </div>
</div>
<style>
.card-header.bg-primary { background: linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important; color:#fff; }
.btn-success { background: linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important; border:none; }
</style>
