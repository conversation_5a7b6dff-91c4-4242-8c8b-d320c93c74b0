<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
// TODO: Ambil data postingan dari database
include __DIR__ . '/inc/header.php';
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';
$posts = function_exists('getAllPosts') ? getAllPosts($conn) : [];
$categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
function getCategoryName($categories, $id) {
    foreach ($categories as $cat) {
        if ($cat['id'] == $id) return $cat['category'];
    }
    return '-';
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Daftar Postingan</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Daftar Postingan</h2>
    <a href="post-add.php" class="btn btn-primary mb-3">Tambah Postingan</a>
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                <th>No</th>
                <th>Judul</th>
                <th>Kategori</th>
                <th>Cover</th>
                <th>Tanggal</th>
                <th>Aksi</th>
            </tr>
        </thead>
        <tbody>
        <?php if (is_array($posts) && count($posts) > 0): $no=1; foreach ($posts as $post): ?>
            <tr>
                <td><?= $no++ ?></td>
                <td><?= htmlspecialchars($post['post_title'] ?? '-') ?></td>
                <td><?= htmlspecialchars(getCategoryName($categories, $post['category_id'] ?? 0)) ?></td>
                <td><?php if (!empty($post['cover_url'])): ?>
                    <img src="../upload/blog/<?= htmlspecialchars($post['cover_url']) ?>" alt="cover" width="60">
                    <?php endif; ?></td>
                <td><?= htmlspecialchars($post['created_at'] ?? '-') ?></td>
                <td>
                    <a href="post-edit.php?id=<?= intval($post['id']) ?>" class="btn btn-sm btn-warning">Edit</a>
                    <a href="post-delete.php?id=<?= intval($post['id']) ?>" class="btn btn-sm btn-danger" onclick="return confirm('Yakin hapus postingan?')">Hapus</a>
                </td>
            </tr>
        <?php endforeach; else: ?>
            <tr><td colspan="6" class="text-center">Belum ada postingan.</td></tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
</body>
</html>
