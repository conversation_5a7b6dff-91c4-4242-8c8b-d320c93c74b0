<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handler
include_once __DIR__ . '/data/comment.php';

// Ambil data komentar dari database
try {
    $comments = function_exists('getAllComments') ? getAllComments($conn) : [];
} catch (Exception $e) {
    $comments = [];
    $error_message = "Error mengambil data komentar: " . $e->getMessage();
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-comments mr-3"></i>Manajemen Komentar
        </h1>
        <div class="btn-group">
            <button class="btn btn-outline-secondary" onclick="filterComments('all')">
                <i class="fa fa-list mr-1"></i>Semua
            </button>
            <button class="btn btn-outline-success" onclick="filterComments('approved')">
                <i class="fa fa-check mr-1"></i>Disetujui
            </button>
            <button class="btn btn-outline-warning" onclick="filterComments('pending')">
                <i class="fa fa-clock-o mr-1"></i>Pending
            </button>
        </div>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fa fa-check-circle mr-2"></i><?= htmlspecialchars($_GET['success']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_GET['error']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-secondary">
            <i class="fa fa-comments mr-2"></i>Daftar Komentar (<?= count($comments) ?> komentar)
        </div>
        <div class="card-body p-0">
            <?php if (count($comments) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: rgba(108,117,125,0.1);">
                        <tr>
                            <th><i class="fa fa-hashtag mr-1"></i>No</th>
                            <th><i class="fa fa-user mr-1"></i>Nama</th>
                            <th><i class="fa fa-comment mr-1"></i>Komentar</th>
                            <th><i class="fa fa-newspaper-o mr-1"></i>Post</th>
                            <th><i class="fa fa-calendar mr-1"></i>Tanggal</th>
                            <th><i class="fa fa-check-circle mr-1"></i>Status</th>
                            <th><i class="fa fa-cogs mr-1"></i>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($comments as $comment): ?>
                        <?php
                        // Ambil judul post
                        try {
                            $stmt = $conn->prepare("SELECT post_title FROM posts WHERE id = ?");
                            $stmt->execute([$comment['post_id'] ?? 0]);
                            $post_title = $stmt->fetchColumn() ?: 'Post tidak ditemukan';
                        } catch (Exception $e) {
                            $post_title = 'Error';
                        }
                        ?>
                        <tr class="comment-row" data-status="<?= $comment['status'] ?? 'pending' ?>">
                            <td><?= $no++ ?></td>
                            <td>
                                <strong><?= htmlspecialchars($comment['nama'] ?? '-') ?></strong>
                                <br>
                                <small class="text-muted"><?= htmlspecialchars($comment['email'] ?? '') ?></small>
                            </td>
                            <td>
                                <div style="max-width: 200px;">
                                    <?= htmlspecialchars(substr($comment['isi'] ?? '', 0, 100)) ?>
                                    <?= strlen($comment['isi'] ?? '') > 100 ? '...' : '' ?>
                                </div>
                            </td>
                            <td>
                                <small><?= htmlspecialchars(substr($post_title, 0, 30)) ?><?= strlen($post_title) > 30 ? '...' : '' ?></small>
                            </td>
                            <td>
                                <?= isset($comment['tanggal']) ? date('d/m/Y', strtotime($comment['tanggal'])) : '-' ?>
                                <br>
                                <small class="text-muted">
                                    <?= isset($comment['tanggal']) ? date('H:i', strtotime($comment['tanggal'])) : '' ?>
                                </small>
                            </td>
                            <td>
                                <?php $status = $comment['status'] ?? 'pending'; ?>
                                <?php if ($status == 'approved'): ?>
                                    <span class="badge badge-success px-2 py-1">
                                        <i class="fa fa-check mr-1"></i>Disetujui
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-warning px-2 py-1">
                                        <i class="fa fa-clock-o mr-1"></i>Pending
                                    </span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <?php if ($status != 'approved'): ?>
                                    <a href="comment-approve.php?id=<?= $comment['id'] ?>" class="btn btn-sm btn-success" title="Setujui">
                                        <i class="fa fa-check"></i>
                                    </a>
                                    <?php endif; ?>
                                    <a href="comment-edit.php?id=<?= $comment['id'] ?>" class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <a href="comment-delete.php?id=<?= $comment['id'] ?>" class="btn btn-sm btn-danger"
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus komentar ini?')" title="Hapus">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fa fa-comments" style="font-size: 4rem; color: #ccc;"></i>
                <h4 class="mt-3 text-muted">Belum ada komentar</h4>
                <p class="text-muted">Komentar akan muncul di sini ketika pengunjung memberikan komentar pada postingan.</p>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);

    // Add hover effects
    $('tbody tr').hover(
        function() { $(this).addClass('table-active'); },
        function() { $(this).removeClass('table-active'); }
    );
});

// Filter comments by status
function filterComments(status) {
    $('.comment-row').show();

    if (status !== 'all') {
        $('.comment-row').each(function() {
            if ($(this).data('status') !== status) {
                $(this).hide();
            }
        });
    }

    // Update button states
    $('.btn-group button').removeClass('btn-secondary btn-success btn-warning').addClass('btn-outline-secondary');

    if (status === 'all') {
        $('button[onclick="filterComments(\'all\')"]').removeClass('btn-outline-secondary').addClass('btn-secondary');
    } else if (status === 'approved') {
        $('button[onclick="filterComments(\'approved\')"]').removeClass('btn-outline-secondary').addClass('btn-success');
    } else if (status === 'pending') {
        $('button[onclick="filterComments(\'pending\')"]').removeClass('btn-outline-secondary').addClass('btn-warning');
    }
}
</script>

</body>
</html>
