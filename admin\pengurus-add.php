<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
include_once 'data/pengurus.php';
include __DIR__ . '/inc/header.php';

$nama = $jabatan = $foto = $instagram = '';
$msg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama = trim($_POST['nama']);
    $jabatan = trim($_POST['jabatan']);
    $instagram = trim($_POST['instagram']);
    $fotoName = '';
    if (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
        $fotoName = time().'_'.basename($_FILES['foto']['name']);
        move_uploaded_file($_FILES['foto']['tmp_name'], '../upload/'.$fotoName);
    }
    // Simpan data ke database secara manual jika addPengurus belum support instagram
    $stmt = $conn->prepare("INSERT INTO pengurus (nama, jabatan, foto, instagram) VALUES (?, ?, ?, ?)");
    $result = $stmt->execute([$nama, $jabatan, $fotoName, $instagram]);
    if ($result) {
        header('Location: pengurus.php');
        exit();
    } else {
        $msg = 'Gagal menambah pengurus!';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tambah Pengurus</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Tambah Pengurus</h2>
    <?php if($msg): ?><div class="alert alert-danger"><?= $msg ?></div><?php endif; ?>
    <form method="post" enctype="multipart/form-data">
        <div class="form-group">
            <label>Nama</label>
            <input type="text" name="nama" class="form-control" required>
        </div>
        <div class="form-group">
            <label>Jabatan</label>
            <input type="text" name="jabatan" class="form-control" required>
        </div>
        <div class="form-group">
            <label>Link Instagram</label>
            <input type="url" name="instagram" class="form-control" placeholder="https://instagram.com/username">
        </div>
        <div class="form-group">
            <label>Foto</label>
            <input type="file" name="foto" class="form-control-file">
        </div>
        <button type="submit" class="btn btn-success">Simpan</button>
        <a href="pengurus.php" class="btn btn-secondary">Kembali</a>
    </form>
</div>
</body>
</html>
