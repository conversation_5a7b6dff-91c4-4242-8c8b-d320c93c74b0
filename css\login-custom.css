/* Custom Login Page Styling */

/* Body styling untuk halaman login */
body.login-page {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    min-height: 100vh;
    font-family: 'Roboto', sans-serif;
    position: relative;
    overflow-x: hidden;
}

body.login-page::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(0,90,153,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    z-index: -1;
    pointer-events: none;
}

/* Login container */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    position: relative;
}

/* Login card */
.login-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0,90,153,0.15);
    padding: 40px;
    width: 100%;
    max-width: 450px;
    animation: fadeInUp 0.6s ease-out;
    position: relative;
    overflow: hidden;
}

.login-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%);
    border-radius: 20px 20px 0 0;
}

/* Login header */
.login-header {
    text-align: center;
    margin-bottom: 30px;
}

.login-header h1 {
    background: linear-gradient(45deg, #005a99, #ff9800);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.login-header p {
    color: #666;
    font-size: 1rem;
    margin: 0;
}

/* Form styling */
.form-group {
    margin-bottom: 25px;
    position: relative;
}

.form-label {
    color: #333;
    font-weight: 600;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.form-label i {
    margin-right: 8px;
    width: 16px;
}

.form-control.modern-input {
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 15px 20px;
    font-size: 16px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
    position: relative;
}

.form-control.modern-input:focus {
    border-color: #005a99;
    box-shadow: 0 0 0 0.2rem rgba(0,90,153,0.25);
    background: rgba(255,255,255,1);
    outline: none;
    transform: translateY(-2px);
}

.form-control.modern-input:hover {
    border-color: #005a99;
    transform: translateY(-1px);
}

/* Password toggle */
.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.password-toggle:hover {
    color: #005a99;
}

/* Login button */
.btn-login {
    background: linear-gradient(45deg, #005a99, #ff9800);
    border: none;
    border-radius: 12px;
    color: #fff;
    font-weight: 600;
    font-size: 16px;
    padding: 15px 30px;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0,90,153,0.3);
    position: relative;
    overflow: hidden;
}

.btn-login::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #ff9800, #005a99);
    transition: left 0.3s ease;
    z-index: -1;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,90,153,0.4);
    color: #fff;
}

.btn-login:hover::before {
    left: 0;
}

.btn-login:active {
    transform: translateY(0);
}

/* Sign up link */
.signup-link {
    text-align: center;
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

.signup-link a {
    color: #005a99;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    position: relative;
}

.signup-link a::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 0;
    height: 2px;
    background: linear-gradient(45deg, #005a99, #ff9800);
    transition: width 0.3s ease;
}

.signup-link a:hover {
    color: #ff9800;
    text-decoration: none;
}

.signup-link a:hover::after {
    width: 100%;
}

/* Alert styling */
.alert {
    border-radius: 12px;
    border: none;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    animation: slideInDown 0.5s ease-out;
    margin-bottom: 25px;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Loading state */
.btn-login.loading {
    pointer-events: none;
    opacity: 0.7;
}

.btn-login.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design */
@media (max-width: 576px) {
    .login-card {
        padding: 30px 20px;
        margin: 10px;
        border-radius: 15px;
    }
    
    .login-header h1 {
        font-size: 2rem;
    }
    
    .form-control.modern-input {
        padding: 12px 15px;
        font-size: 14px;
    }
    
    .btn-login {
        padding: 12px 25px;
        font-size: 14px;
    }
}

@media (max-width: 768px) {
    .login-container {
        padding: 15px;
    }
}

/* Focus trap untuk accessibility */
.login-card:focus-within {
    box-shadow: 0 25px 50px rgba(0,90,153,0.2);
}
