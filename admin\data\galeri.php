<?php
// Fungsi CRUD galeri dengan PDO
function getAllGaleri($conn) {
    $stmt = $conn->prepare("SELECT * FROM galeri ORDER BY tanggal DESC");
    $stmt->execute();
    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}
function getGaleriById($conn, $id) {
    $stmt = $conn->prepare("SELECT * FROM galeri WHERE id = ?");
    $stmt->execute([$id]);
    return $stmt->fetch(PDO::FETCH_ASSOC);
}
function addGaleri($conn, $judul, $gambar, $deskripsi) {
    $tanggal = date('Y-m-d H:i:s');
    $stmt = $conn->prepare("INSERT INTO galeri (judul, gambar, deskripsi, tanggal) VALUES (?, ?, ?, ?)");
    return $stmt->execute([$judul, $gambar, $deskripsi, $tanggal]);
}
function updateGaleri($conn, $id, $judul, $gambar, $deskripsi) {
    if ($gambar) {
        $stmt = $conn->prepare("UPDATE galeri SET judul=?, gambar=?, deskripsi=? WHERE id=?");
        return $stmt->execute([$judul, $gambar, $deskripsi, $id]);
    } else {
        $stmt = $conn->prepare("UPDATE galeri SET judul=?, deskripsi=? WHERE id=?");
        return $stmt->execute([$judul, $deskripsi, $id]);
    }
}
function deleteGaleri($conn, $id) {
    $stmt = $conn->prepare("DELETE FROM galeri WHERE id=?");
    return $stmt->execute([$id]);
}
