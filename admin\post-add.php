<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
// TODO: Proses tambah postingan
include __DIR__ . '/inc/header.php';
include_once __DIR__ . '/data/post.php';
include_once __DIR__ . '/data/category.php';

// Proses tambah postingan
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $content = isset($_POST['content']) ? trim($_POST['content']) : '';
    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
    $cover_url = '';

    // Pastikan folder upload/blog ada
    $uploadDir = realpath(__DIR__ . '/../upload/blog');
    if ($uploadDir === false) {
        $baseUpload = __DIR__ . '/../upload/blog';
        if (!is_dir($baseUpload)) {
            mkdir($baseUpload, 0777, true);
        }
        $uploadDir = realpath($baseUpload);
    }

    if (isset($_FILES['cover']) && $_FILES['cover']['error'] === UPLOAD_ERR_OK) {
        $ext = pathinfo($_FILES['cover']['name'], PATHINFO_EXTENSION);
        $filename = 'post_' . time() . '.' . $ext;
        $target = $uploadDir . DIRECTORY_SEPARATOR . $filename;
        if (move_uploaded_file($_FILES['cover']['tmp_name'], $target)) {
            $cover_url = $filename;
        }
    }
    // Simpan ke database
    $conn = $conn ?? (include __DIR__ . '/../db_conn.php');
    $stmt = $conn->prepare("INSERT INTO post (post_title, post_text, category_id, cover_url, created_at) VALUES (?, ?, ?, ?, NOW())");
    $stmt->execute([$title, $content, $category_id, $cover_url]);
    header('Location: post.php');
    exit();
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tambah Postingan</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Tambah Postingan</h2>
    <form method="post" enctype="multipart/form-data">
        <div class="mb-3">
            <label for="title" class="form-label">Judul</label>
            <input type="text" class="form-control" id="title" name="title" required>
        </div>
        <div class="mb-3">
            <label for="content" class="form-label">Konten</label>
            <textarea class="form-control" id="content" name="content" rows="5" required></textarea>
        </div>
        <div class="mb-3">
            <label for="category_id" class="form-label">Kategori</label>
            <select class="form-control" id="category_id" name="category_id" required>
                <option value="">Pilih Kategori</option>
                <?php 
                $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
                foreach ($categories as $cat) {
                    echo '<option value="'.intval($cat['id']).'">'.htmlspecialchars($cat['category']).'</option>';
                }
                ?>
            </select>
        </div>
        <div class="mb-3">
            <label for="cover" class="form-label">Cover (opsional)</label>
            <input type="file" class="form-control" id="cover" name="cover" accept="image/*">
        </div>
        <button type="submit" class="btn btn-success">Simpan</button>
        <a href="post.php" class="btn btn-secondary">Kembali</a>
    </form>
</div>
</body>
</html>
