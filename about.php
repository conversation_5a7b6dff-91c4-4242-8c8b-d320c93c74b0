<?php
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
    $logged = true;
    $user_id = $_SESSION['user_id'];
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <title>UKM Panahan Universitas Semarang</title>
    <link rel="icon" href="img/logo.jpg" type="Image/x-icon">
    <meta charset="utf-8">
    <meta http-equiv="x-ua-compatible" content="ie=edge">
    <meta name="description" content="">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/font-awesome.min.css">
    <link rel="stylesheet" href="css/style.css">
    <link rel="stylesheet" href="css/responsive.css">
    <style>
body {
    background: #fff !important;
    min-height: 100vh;
}
.singel-publication {
    background: #fff;
    border-radius: 1.2rem;
    box-shadow: 0 4px 24px 0 rgba(46, 54, 80, 0.08);
    margin-bottom: 24px;
    overflow: hidden;
    transition: box-shadow 0.2s, transform 0.2s;
}
.singel-publication:hover {
    box-shadow: 0 8px 32px 0 rgba(46, 54, 80, 0.16);
    transform: translateY(-4px) scale(1.02);
}
.singel-publication .image img {
    width: 100%;
    height: 220px;
    object-fit: cover;
    border-radius: 1.2rem 1.2rem 0 0;
}
.singel-publication .cont {
    padding: 18px 10px 12px 10px;
}
.singel-publication .name h6 {
    font-size: 1.1rem;
    font-weight: 700;
    color: #005a99;
    margin-bottom: 2px;
}
.singel-publication .name span {
    font-size: 0.98rem;
}
.singel-publication .badge {
    background: linear-gradient(90deg, #005a99 0%, #ff9800 100%) !important;
    color: #fff !important;
    border-radius: 1rem;
    font-size: 0.92em;
    font-weight: 500;
    margin-bottom: 0.5em;
}
.singel-publication .btn-primary.btn-sm-square {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    margin: 8px auto 0 auto;
}
/* Responsive header/navbar */
@media (max-width: 991px) {
    .navbar-nav { flex-direction: column !important; align-items: flex-start !important; }
    .navbar-nav .nav-item { margin-left: 0 !important; margin-bottom: 8px; }
    .navbar-brand img { max-width: 120px; height: auto; }
    footer, .footer { text-align: center !important; padding: 18px 6px !important; font-size: 0.98rem; }
}
@media (max-width: 767px) {
    .singel-publication .image img {
        height: 140px;
    }
    .singel-publication .cont {
        padding: 10px 4px 8px 4px;
    }
    .singel-publication .name h6 {
        font-size: 1rem;
    }
    .singel-publication .btn-primary.btn-sm-square {
        width: 28px;
        height: 28px;
        font-size: 1em;
    }
    .navbar-brand img {
        max-width: 90px;
    }
}
@media (max-width: 575px) {
    .singel-publication {
        border-radius: 0.7rem;
    }
    .singel-publication .image img {
        border-radius: 0.7rem 0.7rem 0 0;
        height: 90px;
    }
    .singel-publication .cont {
        padding: 6px 2px 6px 2px;
    }
    .singel-publication .name h6 {
        font-size: 0.95rem;
    }
    .singel-publication .badge {
        font-size: 0.85em;
    }
    .navbar-brand img {
        max-width: 70px;
    }
    .footer, footer {
        font-size: 0.92rem;
        padding: 12px 2px !important;
    }
}
.back-to-top .btn {
    background: linear-gradient(90deg, #005a99 0%, #ff9800 100%) !important;
    color: #fff !important;
    border-radius: 50%;
    width: 38px;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2em;
    box-shadow: 0 2px 8px 0 rgba(10,41,71,0.10);
    transition: background 0.2s;
}
.back-to-top .btn:hover {
    background: linear-gradient(90deg, #ff9800 0%, #005a99 100%) !important;
}
</style>
<!-- ...existing code... -->
<?php
    include 'inc/navbar.php';
    include_once("admin/data/pengurus.php");
    include_once("admin/data/divisi.php");
    include_once("db_conn.php");
    // Gunakan getAllPengurus
    $penguruss = function_exists('getAllPengurus') ? getAllPengurus($conn) : [];
    ?>
   <div class="preloader">
        <div class="loader rubix-cube">
            <div class="layer layer-1"></div>
            <div class="layer layer-2"></div>
            <div class="layer layer-3 color-1"></div>
            <div class="layer layer-4"></div>
            <div class="layer layer-5"></div>
            <div class="layer layer-6"></div>
            <div class="layer layer-7"></div>
            <div class="layer layer-8"></div>
        </div>
    </div>

    <!-- Header End -->

     <section id="shop-page" class="pt-120 pb-120 gray-bg">
         <?php if ($penguruss != 0) { ?>
        <div class="container">
            
            <div class="tab-content" id="myTabContent">
              <div class="tab-pane fade show active" id="shop-grid" role="tabpanel" aria-labelledby="shop-grid-tab">
                    <div class="row justify-content-center">
                          <?php foreach ($penguruss as $pengurus) { 
                        $divisi = function_exists('getdivisiById') ? getdivisiById($conn, $pengurus['divisi']) : null;
                        ?>
                        <div class="col-lg-3 col-md-6 col-sm-8">
                            <div class="singel-publication mt-30">
                                <div class="image">
                                    <img src="<?= !empty($pengurus['foto']) ? '../upload/' . htmlspecialchars($pengurus['foto']) : 'upload/blog/default.jpg' ?>" alt="Publication" onerror="this.onerror=null;this.src='upload/blog/default.jpg';">
                                    <div class="row justify-content-center">
                                    <a href="<?= !empty($pengurus['instagram']) ? htmlspecialchars($pengurus['instagram']) : '#' ?>" class="btn btn-primary btn-sm-square" target="_blank" rel="noopener">
                                        <i class="fa fa-instagram"></i></a>
                                </div>
                                </div>
                                <div class="cont">
                                    <div class="name text-center">
                                        <h6 class="mb-1"><?= isset($pengurus['nama']) ? htmlspecialchars($pengurus['nama']) : '-' ?></h6>
                                        <span class="d-block mb-1" style="font-size:1.05em; color:#555; font-weight:500;"><?= isset($pengurus['jabatan']) ? htmlspecialchars($pengurus['jabatan']) : '-' ?></span>
                                        <?php if ($divisi && isset($divisi['divisi'])): ?>
                                            <span class="badge bg-info text-white mb-1"><?= htmlspecialchars($divisi['divisi']) ?></span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div> <!-- singel publication -->
                        </div>
                                        <?php } ?>
                         <!-- singel publication -->
                        </div>
                    </div> <!-- row -->
                </div>
            </div> 

        </div> 
            <?php } ?><!-- container -->
    </section>
    <!-- Team End -->

    <!-- Footer Start -->
    <?php   include 'inc/footer.php'; ?>
    <!-- Footer End -->
    <script src="js/jquery-1.12.4.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script src="js/main.js"></script>
    <!-- Back to Top -->
    <div class="back-to-top">
        <a href="#" class="btn"><i class="fa fa-arrow-up"></i></a>
    </div>


 
</body>

</html>