<?php
$headerPath = __DIR__ . '/inc/header.php';
if (file_exists($headerPath)) {
    include_once $headerPath;
}
include_once __DIR__ . "/data/galeri.php";
include_once __DIR__ . "/../db_conn.php";
$galeri = getAllGaleri($conn);
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    $item = getGaleriById($conn, $id);
    if ($item && $item['gambar'] && file_exists(__DIR__ . "/../upload/galeri/".$item['gambar'])) {
        unlink(__DIR__ . "/../upload/galeri/".$item['gambar']);
    }
    deleteGaleri($conn, $id);
    header("Location: galeri.php");
    exit;
}
?>
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-3">
        <h2 class="mb-0"><i class="fa fa-image mr-2"></i>Galeri</h2>
        <a href="galeri-add.php" class="btn btn-success"><i class="fa fa-plus"></i> Tambah Galeri</a>
    </div>
    <div class="table-responsive">
    <table class="table table-bordered table-hover bg-white shadow-sm rounded">
        <thead class="thead-dark">
            <tr>
                <th style="width:120px">Media</th>
                <th>Judul</th>
                <th>Deskripsi</th>
                <th style="width:120px">Tanggal</th>
                <th style="width:120px">Aksi</th>
            </tr>
        </thead>
        <tbody>
        <?php foreach($galeri as $g): ?>
            <tr>
                <td class="text-center align-middle">
                    <?php if(preg_match('/\.(mp4|webm|ogg)$/i', $g['gambar'])): ?>
                        <video src="../upload/galeri/<?=htmlspecialchars($g['gambar'])?>" width="90" style="border-radius:8px;box-shadow:0 2px 8px #0001;" controls></video>
                    <?php else: ?>
                        <img src="../upload/galeri/<?=htmlspecialchars($g['gambar'])?>" width="80" style="border-radius:8px;box-shadow:0 2px 8px #0001;">
                    <?php endif; ?>
                </td>
                <td class="align-middle"><b><?=htmlspecialchars($g['judul'])?></b></td>
                <td class="align-middle"><?=htmlspecialchars($g['deskripsi'])?></td>
                <td class="align-middle text-nowrap"><?=htmlspecialchars($g['tanggal'])?></td>
                <td class="align-middle text-center">
                    <a href="galeri-edit.php?id=<?=$g['id']?>" class="btn btn-warning btn-sm"><i class="fa fa-edit"></i> Edit</a>
                    <a href="galeri.php?delete=<?=$g['id']?>" class="btn btn-danger btn-sm" onclick="return confirm('Hapus data?')"><i class="fa fa-trash"></i> Hapus</a>
                </td>
            </tr>
        <?php endforeach; ?>
        </tbody>
    </table>
    </div>
</div>
<style>
.table-bordered th, .table-bordered td { vertical-align: middle !important; }
.table thead th { background: linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important; color:#fff; border:none; }
.btn-success { background: linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important; border:none; }
.btn-warning, .btn-danger { color:#fff; border:none; }
.btn-warning { background: #ff9800 !important; }
.btn-danger { background: #d9534f !important; }
</style>
