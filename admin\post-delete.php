<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: post.php?error=' . urlencode('ID postingan tidak valid'));
    exit();
}

$post_id = intval($_GET['id']);

try {
    // Check if post exists and get cover info
    $stmt = $conn->prepare("SELECT * FROM posts WHERE id = ?");
    $stmt->execute([$post_id]);
    $post = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$post) {
        header('Location: post.php?error=' . urlencode('Postingan tidak ditemukan'));
        exit();
    }

    // Delete cover image if exists
    if (!empty($post['cover_url']) && file_exists(__DIR__ . '/../upload/blog/' . $post['cover_url'])) {
        unlink(__DIR__ . '/../upload/blog/' . $post['cover_url']);
    }

    // Delete related comments first (if any)
    $stmt = $conn->prepare("DELETE FROM comments WHERE post_id = ?");
    $stmt->execute([$post_id]);

    // Delete post
    $stmt = $conn->prepare("DELETE FROM posts WHERE id = ?");
    $result = $stmt->execute([$post_id]);

    if ($result) {
        header('Location: post.php?success=' . urlencode('Postingan berhasil dihapus'));
    } else {
        header('Location: post.php?error=' . urlencode('Gagal menghapus postingan'));
    }

} catch (Exception $e) {
    header('Location: post.php?error=' . urlencode('Error: ' . $e->getMessage()));
}

exit();
