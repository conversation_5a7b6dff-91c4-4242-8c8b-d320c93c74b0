<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: category.php?error=' . urlencode('ID kategori tidak valid'));
    exit();
}

$category_id = intval($_GET['id']);

try {
    // Check if category exists
    $stmt = $conn->prepare("SELECT * FROM categories WHERE id = ?");
    $stmt->execute([$category_id]);
    $category = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$category) {
        header('Location: category.php?error=' . urlencode('Kategori tidak ditemukan'));
        exit();
    }

    // Check if category has posts
    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM posts WHERE category_id = ?");
    $stmt->execute([$category_id]);
    $post_count = $stmt->fetchColumn();

    if ($post_count > 0) {
        header('Location: category.php?error=' . urlencode('Tidak dapat menghapus kategori yang masih memiliki ' . $post_count . ' postingan'));
        exit();
    }

    // Delete category
    $stmt = $conn->prepare("DELETE FROM categories WHERE id = ?");
    $result = $stmt->execute([$category_id]);

    if ($result) {
        header('Location: category.php?success=' . urlencode('Kategori berhasil dihapus'));
    } else {
        header('Location: category.php?error=' . urlencode('Gagal menghapus kategori'));
    }

} catch (Exception $e) {
    header('Location: category.php?error=' . urlencode('Error: ' . $e->getMessage()));
}

exit();
