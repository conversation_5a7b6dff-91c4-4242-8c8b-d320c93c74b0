<?php
session_start();
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Simulasi update ke session, silakan ganti ke query database jika sudah ada
$errors = [];
$success = false;

// Validasi dan update username
if (isset($_POST['username']) && trim($_POST['username']) !== '') {
    $_SESSION['username'] = htmlspecialchars(trim($_POST['username']));
} else {
    $errors[] = 'Nama pengguna tidak boleh kosong.';
}

// Update password jika diisi (simulasi, sebaiknya hash password di database)
if (isset($_POST['password']) && trim($_POST['password']) !== '') {
    // Simulasi update password, implementasikan hash dan update DB jika sudah ada
    $_SESSION['password'] = password_hash($_POST['password'], PASSWORD_DEFAULT);
}

// Upload foto profil jika ada
if (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
    $allowed = ['jpg', 'jpeg', 'png'];
    $maxSize = 2 * 1024 * 1024; // 2MB
    $ext = strtolower(pathinfo($_FILES['foto']['name'], PATHINFO_EXTENSION));
    if (!in_array($ext, $allowed)) {
        $errors[] = 'Format foto harus jpg/jpeg/png.';
    } elseif ($_FILES['foto']['size'] > $maxSize) {
        $errors[] = 'Ukuran foto maksimal 2MB.';
    } else {
        $uploadDir = '../upload/';
        if (!is_dir($uploadDir)) mkdir($uploadDir, 0777, true);
        $newName = 'user_' . time() . '_' . rand(100,999) . '.' . $ext;
        $dest = $uploadDir . $newName;
        if (move_uploaded_file($_FILES['foto']['tmp_name'], $dest)) {
            // Update database kolom profile_pic
            include_once '../db_conn.php';
            $user_id = $_SESSION['user_id'];
            $stmt = $conn->prepare("UPDATE users SET profile_pic = ? WHERE id = ?");
            $stmt->execute([$newName, $user_id]);
            $_SESSION['foto'] = 'upload/' . $newName;
        } else {
            $errors[] = 'Gagal upload foto.';
        }
    }
}

if (empty($errors)) {
    $success = true;
}

// Redirect kembali ke profil dengan pesan
if ($success) {
    header('Location: ../profil.php?msg=update_sukses');
    exit();
} else {
    $msg = urlencode(implode(' ', $errors));
    header('Location: ../profil.php?msg=' . $msg);
    exit();
}
