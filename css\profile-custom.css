/* Custom Profile Page Styling */

/* Definisi btn-login-gradient yang hilang */
.btn-login-gradient {
    background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%) !important;
    color: #fff !important;
    padding: 8px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 8px rgba(0,90,153,0.3);
    display: inline-block;
}

.btn-login-gradient:hover {
    background: linear-gradient(90deg, #ff9800 0%, #005a99 50%, #111 100%) !important;
    color: #fff !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255,152,0,0.4);
    text-decoration: none;
}

/* Body styling untuk halaman profil */
body.profile-page {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    min-height: 100vh;
    font-family: 'Roboto', sans-serif;
    position: relative;
}

body.profile-page::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(0,90,153,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    z-index: -1;
    pointer-events: none;
}

/* Styling untuk halaman profil */
.profile-container {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    animation: fadeInUp 0.6s ease-out;
}

.profile-header {
    background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%) !important;
    color: #fff !important;
    position: relative;
    overflow: hidden;
}

.profile-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.profile-header h3 {
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    font-weight: 700;
}

.profile-avatar {
    position: relative;
    display: inline-block;
}

.profile-avatar::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    background: linear-gradient(45deg, #ff9800, #005a99, #111);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.profile-avatar img {
    border: 4px solid #fff !important;
    box-shadow: 0 8px 25px rgba(0,90,153,0.3);
    transition: all 0.3s ease;
}

.profile-avatar:hover img {
    transform: scale(1.05);
    box-shadow: 0 12px 35px rgba(0,90,153,0.4);
}

/* Form styling */
.modern-input {
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    background: rgba(255,255,255,0.8);
}

.modern-input:focus {
    border-color: #005a99 !important;
    box-shadow: 0 0 0 0.2rem rgba(0,90,153,0.25) !important;
    background: rgba(255,255,255,1) !important;
    outline: none;
}

.btn-gradient-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    border-radius: 25px;
    color: #fff;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(40,167,69,0.3);
}

.btn-gradient-success:hover {
    background: linear-gradient(45deg, #20c997, #28a745) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40,167,69,0.4) !important;
    color: #fff;
}

.btn-outline-gradient {
    border: 2px solid #005a99;
    color: #005a99;
    border-radius: 25px;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    background: transparent;
    position: relative;
    overflow: hidden;
}

.btn-outline-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, #005a99, #ff9800);
    transition: left 0.3s ease;
    z-index: -1;
}

.btn-outline-gradient:hover {
    color: #fff !important;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,90,153,0.3);
    text-decoration: none;
    border-color: #ff9800;
}

.btn-outline-gradient:hover::before {
    left: 0;
}

.btn-gradient-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    border: none;
    border-radius: 25px;
    color: #fff;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
    text-decoration: none;
}

.btn-gradient-danger:hover {
    background: linear-gradient(45deg, #c82333, #dc3545) !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(220,53,69,0.4) !important;
    color: #fff;
    text-decoration: none;
}

/* Card animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(0,90,153,0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(0,90,153,0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0,90,153,0);
    }
}

.profile-avatar:hover::before {
    animation: pulse 1.5s infinite;
}

.profile-info-card, .edit-form-card {
    transition: all 0.3s ease;
}

.profile-info-card:hover, .edit-form-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,90,153,0.15);
}

/* Info items hover effect */
.info-item {
    padding: 8px 0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.info-item:hover {
    background: rgba(0,90,153,0.05);
    padding-left: 10px;
    padding-right: 10px;
}

/* Badge styling */
.badge-gradient {
    background: linear-gradient(45deg, #005a99, #ff9800);
    color: #fff;
    font-size: 12px;
    border-radius: 15px;
}

/* Responsive styling */
@media (max-width: 991px) {
    .profile-avatar img {
        width: 100px !important;
        height: 100px !important;
    }
    
    .btn-outline-gradient, .btn-gradient-danger {
        margin-bottom: 10px;
        width: 100%;
    }
    
    .profile-container {
        margin: 20px 10px;
    }
}

@media (max-width: 576px) {
    .profile-header h3 {
        font-size: 1.5rem;
    }
    
    .profile-info-card, .edit-form-card {
        padding: 20px !important;
    }
    
    .info-item .row .col-4,
    .info-item .row .col-8 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 5px;
    }
}
