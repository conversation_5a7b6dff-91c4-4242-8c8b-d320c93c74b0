<?php 
session_start();
$logged = false;
if (isset($_SESSION['user_id']) && isset($_SESSION['username'])) {
	 $logged = true;
	 $user_id = $_SESSION['user_id'];
    }
  $notFound = 0;
 ?>
<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>
		<?php 
		if(isset($_GET['search'])){ 
			  echo "search '".htmlspecialchars($_GET['search'])."'"; 
		}else{
			echo "Blog Page";
		} ?>
	</title>
	<link rel="stylesheet" href="css/bootstrap.min.css">
	<link rel="stylesheet" href="css/font-awesome.min.css">
	<link rel="stylesheet" href="css/style.css">
	<link rel="stylesheet" href="css/responsive.css">
	<!-- Custom CSS for category/berita page -->
	<style>
	body {
	    background: #fff !important;
	    min-height: 100vh;
	    font-family: 'Poppins', Arial, sans-serif;
	}
	.category-aside {
	    border-radius: 1rem;
	    overflow: hidden;
	    background: #fff;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.08);
	}
	.category-aside .list-group-item {
	    border: none;
	    background: transparent;
	    transition: background 0.2s, color 0.2s;
	    font-weight: 600;
	    color: #005a99;
	    font-size: 1.05rem;
	}
	.category-aside .list-group-item.active,
	.category-aside .list-group-item:active {
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: #fff;
	    border: none;
	    box-shadow: 0 2px 8px #005a9922;
	}
	.category-aside .list-group-item:not(.active):hover {
	    background: #f0f4fa;
	    color: #ff8800;
	}
	.main-blog-card {
	    border-radius: 1.2rem;
	    overflow: hidden;
	    box-shadow: 0 4px 24px 0 rgba(0,90,153,0.10);
	    transition: transform 0.22s, box-shadow 0.22s;
	    background: #fff;
	    border: none;
	    display: flex;
	    flex-direction: column;
	    height: 100%;
	}
	.main-blog-card:hover {
	    transform: translateY(-6px) scale(1.03);
	    box-shadow: 0 12px 36px 0 rgba(0,90,153,0.18);
	}
	.main-blog-card .card-title {
	    font-size: 1.13rem;
	    font-weight: 700;
	    color: #005a99;
	    margin-bottom: 8px;
	    letter-spacing: 0.5px;
	}
	.main-blog-card .card-text {
	    color: #444;
	    font-size: 1.01rem;
	    margin-bottom: 0;
	}
	.main-blog-card .btn-primary {
	    background: linear-gradient(90deg, #005a99 60%, #ff8800 100%);
	    border: none;
	    border-radius: 2rem;
	    font-weight: 600;
	    transition: background 0.2s;
	    letter-spacing: 0.5px;
	}
	.main-blog-card .btn-primary:hover {
	    background: linear-gradient(90deg, #ff8800 0%, #005a99 100%);
	}
	.main-blog-card .card-footer {
	    background: #f8fafc;
	    border-top: none;
	    font-size: 0.97rem;
	    color: #005a99;
	}
	.category-badge, .badge-category {
	    display: inline-block;
	    background: linear-gradient(90deg, #005a99 0%, #ff8800 100%);
	    color: #fff;
	    border-radius: 1rem;
	    padding: 0.32em 1em;
	    font-size: 0.93em;
	    font-weight: 600;
	    margin-bottom: 0.7em;
	    box-shadow: 0 2px 8px #005a9922;
	    letter-spacing: 0.5px;
	}
	.react-btns .fa {
	    cursor: pointer;
	    margin-right: 0.5em;
	    transition: color 0.2s;
	    font-size: 1.1em;
	}
	.react-btns .fa.liked {
	    color: #e74c3c;
	}
	.react-btns .fa:hover {
	    color: #ff8800;
	}
	@media (max-width: 991px) {
	    .category-aside {
	        margin-bottom: 2rem;
	    }
	}
	@media (max-width: 767px) {
	    .navbar-brand img { max-width: 90px; }
	}
	@media (max-width: 575px) {
	    .navbar-brand img { max-width: 70px; }
	    .footer, footer { font-size: 0.92rem; padding: 12px 2px !important; }
	}
	</style>
</head>
<body>
	<?php 
      include 'inc/navbar.php';
      include_once("admin/data/post.php");
      include_once("admin/data/comment.php");
      include_once("admin/data/category.php");
      include_once("admin/data/like.php");
      include_once("db_conn.php");
      if(isset($_GET['category_id'])){
           $cat_id = intval($_GET['category_id']);
           $posts = function_exists('getAllPosts') ? array_filter(getAllPosts($conn), function($p) use ($cat_id) {
               return (isset($p['category_id']) && $p['category_id'] == $cat_id);
           }) : [];
      } else if(isset($_GET['search'])){
           $key = $_GET['search'];
           $posts = function_exists('searchPosts') ? searchPosts($conn, $key) : [];
           if ($posts == 0 || $posts === false) {
                 $notFound = 1;
                 $posts = [];
           }
      }else {
         $posts = function_exists('getAllPosts') ? getAllPosts($conn) : [];
      }
      $categories = function_exists('getAllCategories') ? getAllCategories($conn) : [];
	 ?>
       <div class="preloader">
        <div class="loader rubix-cube">
            <div class="layer layer-1"></div>
            <div class="layer layer-2"></div>
            <div class="layer layer-3 color-1"></div>
            <div class="layer layer-4"></div>
            <div class="layer layer-5"></div>
            <div class="layer layer-6"></div>
            <div class="layer layer-7"></div>
            <div class="layer layer-8"></div>
        </div>
    </div>
    <div class="container mt-5">
    <section class="row">
        <aside class="col-lg-3 col-md-4 mb-4 order-1 order-md-1">
            <div class="list-group category-aside shadow-sm mb-4">
                <a href="#" class="list-group-item list-group-item-action active text-center" aria-current="true">
                    <i class="fa fa-list"></i> Kategori
                </a>
                <a href="category.php" class="list-group-item list-group-item-action<?= !isset($_GET['category_id']) ? ' active' : '' ?>">
                     Semua Kategori
                </a>
                <?php foreach (
                    $categories as $category ) { ?>
                <a href="category.php?category_id=<?= isset($category['id']) ? intval($category['id']) : 0 ?>" 
                   class="list-group-item list-group-item-action<?= (isset($_GET['category_id']) && intval($_GET['category_id']) == intval($category['id'])) ? ' active' : '' ?>">
                    <?= isset($category['category']) ? htmlspecialchars($category['category']) : '-' ?>
                </a>
                <?php } ?>
            </div>
            <!-- Berita terbaru autoslider, hanya gambar, dipindah ke bawah kategori -->
            <div class="card border-0 mb-4 h-100" style="width:100%;height:340px;overflow:hidden;background:transparent !important;box-shadow:none !important;border:none !important;">
                <div class="card-header bg-gradient text-white py-2 px-3 d-flex align-items-center" style="font-size:1.08rem;border-top-left-radius:18px;border-top-right-radius:18px;background:linear-gradient(90deg,#005a99 60%,#ff8800 100%);box-shadow:0 2px 8px #005a9922;letter-spacing:0.5px;">
                    <i class="fa fa-bolt me-2"></i> <span style="font-weight:600;">Berita Terbaru</span>
                </div>
                <div id="latestNewsSlider" class="carousel slide h-100" data-bs-ride="carousel" data-bs-interval="3000">
                  <div class="carousel-inner" style="height:240px;">
                    <?php 
                    $latestPosts = function_exists('getLatestPosts') ? getLatestPosts($conn, 6) : (function_exists('getAllPosts') ? array_slice(getAllPosts($conn), 0, 6) : []);
                    $active = 'active';
                    foreach($latestPosts as $news) { ?>
                      <div class="carousel-item <?= $active ?>" style="height:300px;background:transparent !important;">
                        <a href="blog-view.php?post_id=<?= isset($news['post_id']) ? intval($news['post_id']) : (isset($news['id']) ? intval($news['id']) : 0) ?>" class="d-block w-100 h-100" style="height:300px;">
                            <img src="upload/blog/<?= isset($news['cover_url']) ? htmlspecialchars($news['cover_url']) : '' ?>" alt="" style="width:100%;height:240px;object-fit:cover;border-radius:12px;box-shadow:none !important;transition:transform 0.22s;display:block;margin:auto;background:transparent !important;" onmouseover="this.style.transform='scale(1.04)'" onmouseout="this.style.transform='scale(1)'" />
                        </a>
                      </div>
                    <?php $active = ''; } ?>
                  </div>
                
                </div>
            </div>
        </aside>
        <main class="col-lg-9 col-md-8 order-2 order-md-2 position-relative">
            <div class="row">
            <?php if (is_array($posts) && count($posts) > 0) { ?>
            <?php foreach ($posts as $post) { 
                $post_id = isset($post['post_id']) ? $post['post_id'] : (isset($post['id']) ? $post['id'] : 0);
                $category_name = isset($post['category_name']) ? $post['category_name'] : (isset($post['category']) ? $post['category'] : null);
            ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100 main-blog-card shadow-sm">
                        <?php if ($category_name) { ?>
                        <span class="category-badge"><?= htmlspecialchars($category_name) ?></span>
                        <?php } ?>
                        <img src="upload/blog/<?= isset($post['cover_url']) ? htmlspecialchars($post['cover_url']) : '' ?>" class="card-img-top" alt="...">
                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title text-truncate" title="<?= isset($post['post_title']) ? htmlspecialchars($post['post_title']) : '-' ?>">
                                <?= isset($post['post_title']) ? htmlspecialchars($post['post_title']) : '-' ?>
                            </h5>
                            <?php 
                                $post_text = isset($post['post_text']) ? $post['post_text'] : '';
                                $p = strip_tags($post_text); 
                                $p = substr($p, 0, 120);               
                            ?>
                            <p class="card-text small flex-grow-1"><?= htmlspecialchars($p) ?>...</p>
                            <a href="blog-view.php?post_id=<?= intval($post_id) ?>" class="btn btn-primary btn-sm mt-auto" style="border-radius: 8px; background: linear-gradient(90deg, #1e3c72 60%, #ff7e5f 100%); border: none;">Read more</a>
                        </div>
                        <div class="card-footer bg-white border-0 d-flex justify-content-between align-items-center">
                            <div class="react-btns">
                                <i class="fa fa-thumbs-up<?= ($logged && isLikedByUserID($conn, $post_id, $user_id)) ? ' liked' : '' ?> like-btn" 
                                   post-id="<?= intval($post_id) ?>" liked="<?= ($logged && isLikedByUserID($conn, $post_id, $user_id)) ? '1' : '0' ?>" aria-hidden="true"></i>
                                Likes (<span><?= likeCountByPostID($conn, $post_id) ?></span>)
                                <a href="blog-view.php?post_id=<?= intval($post_id) ?>#comments" class="ms-2">
                                    <i class="fa fa-comment" aria-hidden="true"></i> Comments (<?= CountByPostID($conn, $post_id) ?>)
                                </a>
                            </div>
                            <small class="text-muted ms-2"><i class="fa fa-calendar"></i> <?= isset($post['created_at']) ? htmlspecialchars($post['created_at']) : '' ?></small>
                        </div>
                    </div>
                </div>
            <?php } ?>
            <?php } else { ?>
            <div class="alert alert-warning mt-3"> 
                <?php if($notFound){ ?>
                    No search results found - <b>key = '<?= htmlspecialchars($_GET['search']) ?>'</b>
                <?php } else { ?>
                    No posts yet.
                <?php } ?>
            </div>
            <?php } ?>
            </div>
        </main>
    </section>
    </div>
  
   <script>
   	 $(document).ready(function(){
			  $(".like-btn").click(function(){
			     var post_id = $(this).attr('post-id');
			     var liked = $(this).attr('liked');

			     if (liked == 1) {
                 $(this).attr('liked', '0');
                 $(this).removeClass('liked');
			     }else {
                 $(this).attr('liked', '1');
                 $(this).addClass('liked');
			     }
			     $(this).next().load("ajax/like-unlike.php",
			     	{
			     		post_id: post_id
			     	});
			  });
		  });
   </script>
   <script src="js/jquery-1.12.4.min.js"></script>
   <script src="js/bootstrap.bundle.min.js"></script>
   <script src="js/main.js"></script>
   <script>
   // Force Bootstrap carousel to auto-slide (fix for stuck carousel)
   document.addEventListener('DOMContentLoaded', function() {
      var el = document.querySelector('#latestNewsSlider');
      if (el && typeof bootstrap !== 'undefined' && bootstrap.Carousel) {
         var carousel = bootstrap.Carousel.getOrCreateInstance(el, {interval: 3000, ride: 'carousel'});
         carousel.cycle();
      }
   });
   </script>
   <?php   include 'inc/footer.php'; ?>
</body>
</html>