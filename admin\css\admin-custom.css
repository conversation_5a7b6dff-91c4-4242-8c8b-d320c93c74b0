/* Admin Panel Custom Styling - <PERSON><PERSON><PERSON> dengan Front-end */

/* Body dan Background */
body {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
    font-family: 'Roboto', sans-serif;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="rgba(0,90,153,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
    z-index: -1;
    pointer-events: none;
}

/* Dashboard Content */
.dashboard-content {
    margin: 40px auto;
    padding: 40px 24px;
    max-width: 1400px;
    min-height: 80vh;
    animation: fadeInUp 0.6s ease-out;
}

/* Welcome Header */
.welcome-header {
    background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 700;
    font-size: 2.2rem;
    margin-bottom: 2rem;
    text-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Admin Cards */
.admin-card {
    border: none;
    border-radius: 20px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 20px rgba(0,90,153,0.1);
    position: relative;
    overflow: hidden;
}

.admin-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #111 0%, #005a99 50%, #ff9800 100%);
    border-radius: 20px 20px 0 0;
}

.admin-card:hover {
    box-shadow: 0 8px 35px rgba(0,90,153,0.2);
    transform: translateY(-8px) scale(1.02);
}

/* Admin Icons dengan Brand Colors */
.admin-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.2rem;
    margin: 20px auto 18px auto;
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    position: relative;
    overflow: hidden;
}

.admin-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #ff9800, #005a99, #111);
    border-radius: 50%;
    z-index: -1;
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Specific Icon Colors */
.icon-pengurus { background: linear-gradient(135deg, #005a99 0%, #0066cc 100%); }
.icon-users { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
.icon-posts { background: linear-gradient(135deg, #ff9800 0%, #ffb74d 100%); }
.icon-categories { background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%); }
.icon-comments { background: linear-gradient(135deg, #6c757d 0%, #868e96 100%); }
.icon-logout { background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%); }

/* Buttons dengan Brand Colors */
.btn-brand-primary {
    background: linear-gradient(45deg, #005a99, #0066cc);
    border: none;
    color: #fff;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,90,153,0.3);
}

.btn-brand-primary:hover {
    background: linear-gradient(45deg, #0066cc, #005a99);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,90,153,0.4);
    color: #fff;
}

.btn-brand-success {
    background: linear-gradient(45deg, #28a745, #20c997);
    border: none;
    color: #fff;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(40,167,69,0.3);
}

.btn-brand-success:hover {
    background: linear-gradient(45deg, #20c997, #28a745);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(40,167,69,0.4);
    color: #fff;
}

.btn-brand-warning {
    background: linear-gradient(45deg, #ff9800, #ffb74d);
    border: none;
    color: #fff;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(255,152,0,0.3);
}

.btn-brand-warning:hover {
    background: linear-gradient(45deg, #ffb74d, #ff9800);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(255,152,0,0.4);
    color: #fff;
}

.btn-brand-info {
    background: linear-gradient(45deg, #17a2b8, #20c997);
    border: none;
    color: #fff;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(23,162,184,0.3);
}

.btn-brand-info:hover {
    background: linear-gradient(45deg, #20c997, #17a2b8);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(23,162,184,0.4);
    color: #fff;
}

.btn-brand-danger {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    border: none;
    color: #fff;
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(220,53,69,0.3);
}

.btn-brand-danger:hover {
    background: linear-gradient(45deg, #e74c3c, #dc3545);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220,53,69,0.4);
    color: #fff;
}

/* Summary Cards */
.summary-card {
    background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255,255,255,0.2);
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0,90,153,0.1);
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 30px rgba(0,90,153,0.15);
}

/* Card Headers dengan Brand Colors */
.card-header-brand-primary {
    background: linear-gradient(90deg, #005a99 0%, #0066cc 100%) !important;
    color: #fff !important;
    border-radius: 20px 20px 0 0 !important;
    font-weight: 600;
    border: none;
}

.card-header-brand-success {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%) !important;
    color: #fff !important;
    border-radius: 20px 20px 0 0 !important;
    font-weight: 600;
    border: none;
}

.card-header-brand-warning {
    background: linear-gradient(90deg, #ff9800 0%, #ffb74d 100%) !important;
    color: #fff !important;
    border-radius: 20px 20px 0 0 !important;
    font-weight: 600;
    border: none;
}

.card-header-brand-info {
    background: linear-gradient(90deg, #17a2b8 0%, #20c997 100%) !important;
    color: #fff !important;
    border-radius: 20px 20px 0 0 !important;
    font-weight: 600;
    border: none;
}

.card-header-brand-secondary {
    background: linear-gradient(90deg, #6c757d 0%, #868e96 100%) !important;
    color: #fff !important;
    border-radius: 20px 20px 0 0 !important;
    font-weight: 600;
    border: none;
}

/* Tables */
.summary-table {
    background: #fff;
    border-radius: 0 0 20px 20px;
}

.summary-table th {
    background: rgba(0,90,153,0.1);
    color: #005a99;
    font-weight: 600;
    border: none;
    font-size: 0.9rem;
}

.summary-table td {
    border: 1px solid rgba(0,90,153,0.1);
    font-size: 0.85rem;
    vertical-align: middle;
}

.summary-table tbody tr:hover {
    background: rgba(0,90,153,0.05);
}

/* Summary Title */
.summary-title {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 8px;
    color: #333;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive Design */
@media (max-width: 991px) {
    .dashboard-content { 
        margin: 24px 4px; 
        padding: 16px 2px; 
    }
    
    .welcome-header {
        font-size: 1.8rem;
    }
    
    .admin-icon {
        width: 60px;
        height: 60px;
        font-size: 1.8rem;
    }
}

@media (max-width: 600px) {
    .dashboard-content { 
        margin: 8px 0; 
        padding: 8px 0; 
    }
    
    .welcome-header {
        font-size: 1.5rem;
    }
    
    .admin-card {
        margin-bottom: 15px;
    }
    
    .admin-icon {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
        margin: 15px auto 12px auto;
    }
    
    .summary-title {
        font-size: 1rem;
    }
}

/* Form Enhancements */
.form-control:focus {
    border-color: #005a99;
    box-shadow: 0 0 0 0.2rem rgba(0,90,153,0.25);
}

.custom-file-input:focus ~ .custom-file-label {
    border-color: #005a99;
    box-shadow: 0 0 0 0.2rem rgba(0,90,153,0.25);
}

.form-control.is-invalid {
    border-color: #dc3545;
    box-shadow: 0 0 0 0.2rem rgba(220,53,69,0.25);
}

.form-control.is-valid {
    border-color: #28a745;
    box-shadow: 0 0 0 0.2rem rgba(40,167,69,0.25);
}

.invalid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #dc3545;
}

.valid-feedback {
    display: block;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: #28a745;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Preview Styles */
#imagePreview img, #videoPreview video {
    transition: all 0.3s ease;
}

#imagePreview img:hover, #videoPreview video:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Character Counter */
.char-counter {
    font-size: 0.8rem;
    float: right;
    margin-top: 5px;
}

/* Icon Styles for Dashboard */
.icon-pengurus {
    background: linear-gradient(135deg, #005a99, #0066cc);
    color: #fff;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 15px;
    position: relative;
    overflow: hidden;
}

.icon-users {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: #fff;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 15px;
}

.icon-posts {
    background: linear-gradient(135deg, #ff9800, #ffb74d);
    color: #fff;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 15px;
}

.icon-categories {
    background: linear-gradient(135deg, #17a2b8, #20c997);
    color: #fff;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 15px;
}

.icon-comments {
    background: linear-gradient(135deg, #6c757d, #868e96);
    color: #fff;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 15px;
}

.icon-logout {
    background: linear-gradient(135deg, #dc3545, #e74c3c);
    color: #fff;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 15px;
}

/* Danger Button Style */
.btn-brand-danger {
    background: linear-gradient(45deg, #dc3545, #e74c3c);
    border: none;
    color: #fff;
    border-radius: 25px;
    font-weight: 600;
    padding: 8px 20px;
    transition: all 0.3s ease;
}

.btn-brand-danger:hover {
    background: linear-gradient(45deg, #e74c3c, #dc3545);
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(220,53,69,0.3);
    color: #fff;
}

/* Additional Mobile Styles */
@media (max-width: 768px) {
    .form-group {
        margin-bottom: 1rem;
    }

    .btn-brand-primary,
    .btn-brand-success,
    .btn-brand-warning,
    .btn-brand-info,
    .btn-brand-secondary,
    .btn-brand-danger {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .table-responsive {
        font-size: 0.9rem;
    }

    .btn-group .btn {
        padding: 4px 8px;
        font-size: 0.8rem;
    }

    .admin-icon,
    .icon-pengurus,
    .icon-users,
    .icon-posts,
    .icon-categories,
    .icon-comments,
    .icon-logout {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}
