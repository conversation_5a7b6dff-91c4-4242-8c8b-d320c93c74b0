<?php
$headerPath = __DIR__ . '/inc/header.php';
if (file_exists($headerPath)) {
    include_once $headerPath;
}
include_once __DIR__ . "/data/galeri.php";
include_once __DIR__ . "/../db_conn.php";
$id = intval($_GET['id'] ?? 0);
$data = getGaleriById($conn, $id);
if (!$data) { echo 'Data tidak ditemukan.'; exit; }
$msg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $judul = $_POST['judul'];
    $deskripsi = $_POST['deskripsi'];
    $gambar = $data['gambar'];
    if (!empty($_FILES['gambar']['name'])) {
        $ext = pathinfo($_FILES['gambar']['name'], PATHINFO_EXTENSION);
        $namaFile = time().'_'.rand(100,999).'.'.$ext;
        $tujuan = __DIR__ . "/../upload/galeri/".$namaFile;
        if (move_uploaded_file($_FILES['gambar']['tmp_name'], $tujuan)) {
            if ($gambar && file_exists(__DIR__ . "/../upload/galeri/".$gambar)) unlink(__DIR__ . "/../upload/galeri/".$gambar);
            $gambar = $namaFile;
        }
    }
    if (updateGaleri($conn, $id, $judul, $gambar, $deskripsi)) {
        header("Location: galeri.php"); exit;
    } else {
        $msg = 'Gagal update galeri!';
    }
}
?>
<div class="container mt-4">
    <div class="card shadow-sm mx-auto" style="max-width:520px;">
        <div class="card-header bg-primary text-white" style="background:linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important;">
            <h4 class="mb-0"><i class="fa fa-edit mr-2"></i>Edit Galeri</h4>
        </div>
        <div class="card-body">
            <form method="post" enctype="multipart/form-data">
                <div class="form-group">
                    <label>Judul</label>
                    <input type="text" name="judul" class="form-control" value="<?=htmlspecialchars($data['judul'])?>" required>
                </div>
                <div class="form-group">
                    <label>Deskripsi</label>
                    <textarea name="deskripsi" class="form-control" required><?=htmlspecialchars($data['deskripsi'])?></textarea>
                </div>
                <div class="form-group">
                    <label>Media Saat Ini</label><br>
                    <?php if(preg_match('/\.(mp4|webm|ogg)$/i', $data['gambar'])): ?>
                        <video src="../upload/galeri/<?=htmlspecialchars($data['gambar'])?>" width="120" style="border-radius:8px;box-shadow:0 2px 8px #0001;" controls></video><br>
                    <?php else: ?>
                        <img src="../upload/galeri/<?=htmlspecialchars($data['gambar'])?>" width="120" style="border-radius:8px;box-shadow:0 2px 8px #0001;"><br>
                    <?php endif; ?>
                    <input type="file" name="gambar" class="form-control-file" accept="image/*,video/*">
                    <small>Kosongkan jika tidak ingin ganti media</small>
                </div>
                <button type="submit" class="btn btn-primary">Update</button>
                <a href="galeri.php" class="btn btn-secondary">Kembali</a>
                <div class="text-danger mt-2"><?=$msg?></div>
            </form>
        </div>
    </div>
</div>
<style>
.card-header.bg-primary { background: linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important; color:#fff; }
.btn-primary { background: linear-gradient(90deg,#0a2947 0%,#ff9800 100%) !important; border:none; }
</style>
