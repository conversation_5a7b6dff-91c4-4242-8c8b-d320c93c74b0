<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
include_once 'data/pengurus.php';
include __DIR__ . '/inc/header.php';

$id = isset($_GET['id']) ? intval($_GET['id']) : 0;
$pengurus = getPengurusById($conn, $id);
if (!$pengurus) { header('Location: pengurus.php'); exit(); }
$msg = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nama = trim($_POST['nama']);
    $jabatan = trim($_POST['jabatan']);
    $fotoName = $pengurus['foto'];
    if (isset($_FILES['foto']) && $_FILES['foto']['error'] === UPLOAD_ERR_OK) {
        $fotoName = time().'_'.basename($_FILES['foto']['name']);
        move_uploaded_file($_FILES['foto']['tmp_name'], '../upload/'.$fotoName);
    }
    if (updatePengurus($conn, $id, $nama, $jabatan, $fotoName)) {
        header('Location: pengurus.php');
        exit();
    } else {
        $msg = 'Gagal update pengurus!';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Edit Pengurus</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Edit Pengurus</h2>
    <?php if($msg): ?><div class="alert alert-danger"><?= $msg ?></div><?php endif; ?>
    <form method="post" enctype="multipart/form-data">
        <div class="form-group">
            <label>Nama</label>
            <input type="text" name="nama" class="form-control" value="<?= htmlspecialchars($pengurus['nama']) ?>" required>
        </div>
        <div class="form-group">
            <label>Jabatan</label>
            <input type="text" name="jabatan" class="form-control" value="<?= isset($pengurus['jabatan']) ? htmlspecialchars($pengurus['jabatan']) : '' ?>" required>
        </div>
        <div class="form-group">
            <label>Foto</label><br>
            <?php if($pengurus['foto']): ?><img src="../upload/<?= htmlspecialchars($pengurus['foto']) ?>" width="80"><br><?php endif; ?>
            <input type="file" name="foto" class="form-control-file">
        </div>
        <button type="submit" class="btn btn-success">Update</button>
        <a href="pengurus.php" class="btn btn-secondary">Kembali</a>
    </form>
</div>
</body>
</html>
