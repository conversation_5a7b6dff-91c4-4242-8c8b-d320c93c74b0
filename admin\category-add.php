<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
// TODO: Proses tambah kategori
include __DIR__ . '/inc/header.php';
include_once __DIR__ . '/data/category.php';
$conn = $conn ?? (include __DIR__ . '/../db_conn.php');

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $category = isset($_POST['category']) ? trim($_POST['category']) : '';
    if ($category !== '') {
        $stmt = $conn->prepare("INSERT INTO category (category) VALUES (?)");
        $stmt->execute([$category]);
        header('Location: category.php');
        exit();
    } else {
        $error = 'Nama kategori tidak boleh kosong!';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Tambah Kategori</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Tambah Kategori</h2>
    <form method="post">
        <div class="mb-3">
            <label for="category" class="form-label">Nama Kategori</label>
            <input type="text" class="form-control" id="category" name="category" required>
        </div>
        <button type="submit" class="btn btn-success">Simpan</button>
        <a href="category.php" class="btn btn-secondary">Kembali</a>
    </form>
</div>
</body>
</html>
