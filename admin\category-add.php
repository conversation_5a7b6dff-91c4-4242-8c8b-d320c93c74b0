<?php
// Include header (sudah ada session check dan DB connection)
include __DIR__ . '/inc/header.php';

// Include data handler
include_once __DIR__ . '/data/category.php';

$errors = [];
$success = false;

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $category = trim($_POST['category'] ?? '');
    $description = trim($_POST['description'] ?? '');
    $color = trim($_POST['color'] ?? '#17a2b8');

    // Validation
    if (empty($category)) {
        $errors[] = 'Nama kategori tidak boleh kosong';
    } elseif (strlen($category) < 3) {
        $errors[] = 'Nama kategori minimal 3 karakter';
    } elseif (strlen($category) > 50) {
        $errors[] = 'Nama kategori maksimal 50 karakter';
    }

    // Check if category already exists
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("SELECT id FROM categories WHERE category = ?");
            $stmt->execute([$category]);
            if ($stmt->fetch()) {
                $errors[] = 'Kategori dengan nama tersebut sudah ada';
            }
        } catch (Exception $e) {
            $errors[] = 'Error checking existing category: ' . $e->getMessage();
        }
    }

    // Insert to database
    if (empty($errors)) {
        try {
            $stmt = $conn->prepare("INSERT INTO categories (category, description, color, created_at) VALUES (?, ?, ?, NOW())");
            $result = $stmt->execute([$category, $description, $color]);

            if ($result) {
                header('Location: category.php?success=' . urlencode('Kategori berhasil ditambahkan'));
                exit();
            } else {
                $errors[] = 'Gagal menambahkan kategori ke database';
            }
        } catch (Exception $e) {
            $errors[] = 'Error: ' . $e->getMessage();
        }
    }
}
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-plus mr-3"></i>Tambah Kategori Baru
        </h1>
        <a href="category.php" class="btn btn-secondary">
            <i class="fa fa-arrow-left mr-2"></i>Kembali
        </a>
    </div>

    <?php if (!empty($errors)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i>
        <ul class="mb-0">
            <?php foreach ($errors as $error): ?>
            <li><?= htmlspecialchars($error) ?></li>
            <?php endforeach; ?>
        </ul>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-info">
            <i class="fa fa-plus mr-2"></i>Form Tambah Kategori
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-8">
                        <div class="form-group">
                            <label for="category"><i class="fa fa-tag mr-1"></i>Nama Kategori *</label>
                            <input type="text" class="form-control" id="category" name="category"
                                   value="<?= htmlspecialchars($_POST['category'] ?? '') ?>"
                                   placeholder="Masukkan nama kategori" required>
                            <small class="form-text text-muted">3-50 karakter, harus unik</small>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="color"><i class="fa fa-paint-brush mr-1"></i>Warna Kategori</label>
                            <div class="input-group">
                                <input type="color" class="form-control" id="color" name="color"
                                       value="<?= htmlspecialchars($_POST['color'] ?? '#17a2b8') ?>"
                                       style="height: 38px;">
                                <div class="input-group-append">
                                    <span class="input-group-text">
                                        <i class="fa fa-palette"></i>
                                    </span>
                                </div>
                            </div>
                            <small class="form-text text-muted">Warna untuk badge kategori</small>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description"><i class="fa fa-info-circle mr-1"></i>Deskripsi</label>
                    <textarea class="form-control" id="description" name="description" rows="3"
                              placeholder="Deskripsi singkat tentang kategori ini (opsional)"><?= htmlspecialchars($_POST['description'] ?? '') ?></textarea>
                    <small class="form-text text-muted">Deskripsi akan membantu pengguna memahami kategori ini</small>
                </div>

                <div class="form-group">
                    <label><i class="fa fa-eye mr-1"></i>Preview</label>
                    <div class="preview-container p-3" style="background: #f8f9fa; border-radius: 8px; border: 1px solid #dee2e6;">
                        <span class="badge px-3 py-2" id="categoryPreview" style="background-color: #17a2b8; color: #fff; font-size: 1rem;">
                            <i class="fa fa-tag mr-1"></i>
                            <span id="previewText">Nama Kategori</span>
                        </span>
                        <p class="mt-2 mb-0 text-muted" id="previewDesc">Deskripsi kategori akan muncul di sini</p>
                    </div>
                </div>

                <div class="form-group text-right">
                    <a href="category.php" class="btn btn-secondary mr-2">
                        <i class="fa fa-times mr-1"></i>Batal
                    </a>
                    <button type="submit" class="btn btn-brand-info">
                        <i class="fa fa-save mr-1"></i>Simpan Kategori
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Live preview
    function updatePreview() {
        var name = $('#category').val() || 'Nama Kategori';
        var desc = $('#description').val() || 'Deskripsi kategori akan muncul di sini';
        var color = $('#color').val();

        $('#previewText').text(name);
        $('#previewDesc').text(desc);
        $('#categoryPreview').css('background-color', color);

        // Adjust text color based on background
        var rgb = hexToRgb(color);
        var brightness = (rgb.r * 299 + rgb.g * 587 + rgb.b * 114) / 1000;
        $('#categoryPreview').css('color', brightness > 128 ? '#000' : '#fff');
    }

    function hexToRgb(hex) {
        var result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? {
            r: parseInt(result[1], 16),
            g: parseInt(result[2], 16),
            b: parseInt(result[3], 16)
        } : null;
    }

    // Update preview on input
    $('#category, #description, #color').on('input', updatePreview);

    // Initial preview
    updatePreview();

    // Category name validation
    $('#category').on('input', function() {
        var value = $(this).val();
        var length = value.length;

        if (length < 3 && length > 0) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Nama kategori minimal 3 karakter</div>');
            }
        } else if (length > 50) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">Nama kategori maksimal 50 karakter</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        var isValid = true;
        var category = $('#category').val().trim();

        if (!category || category.length < 3 || category.length > 50) {
            $('#category').addClass('is-invalid');
            isValid = false;
        } else {
            $('#category').removeClass('is-invalid');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Mohon periksa kembali nama kategori!');
        }
    });
});
</script>

</body>
</html>
