<?php
session_start();
if (!isset($_SESSION['username'])) { header('Location: ../login.php'); exit(); }
include_once 'data/pengurus.php';
$pengurus = getAllPengurus($conn);
include __DIR__ . '/inc/header.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Daftar Pengurus</title>
    <link rel="stylesheet" href="../css/bootstrap.min.css">
</head>
<body>
<div class="container mt-5">
    <h2>Daftar Pengurus</h2>
    <a href="pengurus-add.php" class="btn btn-primary mb-3">Tambah Pengurus</a>
    <table class="table table-bordered table-hover">
        <thead class="thead-dark">
            <tr>
                <th>No</th>
                <th>Nama</th>
                <th>Jabatan</th>
                <th>Instagram</th>
                <th>Foto</th>
                <th>Aksi</th>
            </tr>
        </thead>
        <tbody>
        <?php if ($pengurus && count($pengurus) > 0): $no=1; foreach($pengurus as $p): ?>
            <tr>
                <td><?= $no++ ?></td>
                <td><?= htmlspecialchars($p['nama']) ?></td>
                <td><?= isset($p['jabatan']) ? htmlspecialchars($p['jabatan']) : '-' ?></td>
                <td>
                    <?php if(!empty($p['instagram'])): ?>
                        <a href="<?= htmlspecialchars($p['instagram']) ?>" target="_blank" rel="noopener">Instagram</a>
                    <?php else: ?>
                        -
                    <?php endif; ?>
                </td>
                <td><?php if($p['foto']): ?><img src="../upload/<?= htmlspecialchars($p['foto']) ?>" width="60"/><?php endif; ?></td>
                <td>
                    <a href="pengurus-edit.php?id=<?= $p['id'] ?>" class="btn btn-warning btn-sm">Edit</a>
                    <a href="pengurus-delete.php?id=<?= $p['id'] ?>" class="btn btn-danger btn-sm" onclick="return confirm('Yakin hapus?')">Hapus</a>
                </td>
            </tr>
        <?php endforeach; else: ?>
            <tr><td colspan="6" class="text-center">Belum ada data pengurus.</td></tr>
        <?php endif; ?>
        </tbody>
    </table>
</div>
</body>
</html>
