<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Handle delete action
if (isset($_GET['delete'])) {
    $id = intval($_GET['delete']);
    try {
        // Check if jabatan is being used by pengurus
        // First get the jabatan name
        $stmt = $conn->prepare("SELECT nama_jabatan FROM jabatan WHERE id = ?");
        $stmt->execute([$id]);
        $jabatan_name = $stmt->fetchColumn();

        if ($jabatan_name) {
            // Then check usage with explicit collation
            $stmt = $conn->prepare("SELECT COUNT(*) as count FROM pengurus WHERE jabatan COLLATE utf8mb4_general_ci = ? COLLATE utf8mb4_general_ci");
            $stmt->execute([$jabatan_name]);
            $usage_count = $stmt->fetchColumn();
        } else {
            $usage_count = 0;
        }
        
        if ($usage_count > 0) {
            header('Location: jabatan.php?error=' . urlencode('Tidak dapat menghapus jabatan yang sedang digunakan oleh ' . $usage_count . ' pengurus'));
            exit();
        }
        
        // Delete jabatan
        $stmt = $conn->prepare("DELETE FROM jabatan WHERE id = ?");
        $result = $stmt->execute([$id]);
        
        if ($result) {
            header('Location: jabatan.php?success=' . urlencode('Jabatan berhasil dihapus'));
        } else {
            header('Location: jabatan.php?error=' . urlencode('Gagal menghapus jabatan'));
        }
    } catch (Exception $e) {
        header('Location: jabatan.php?error=' . urlencode('Error: ' . $e->getMessage()));
    }
    exit();
}

// Get all jabatan
try {
    $stmt = $conn->query("SELECT * FROM jabatan ORDER BY urutan ASC, nama_jabatan ASC");
    $jabatan_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $jabatan_list = [];
    $error_message = "Error mengambil data jabatan: " . $e->getMessage();
}

// Include header setelah semua redirect logic
include __DIR__ . '/inc/header.php';
?>

<div class="dashboard-content">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="welcome-header">
            <i class="fa fa-briefcase mr-3"></i>Manajemen Jabatan
        </h1>
        <a href="jabatan-add.php" class="btn btn-brand-primary">
            <i class="fa fa-plus mr-2"></i>Tambah Jabatan
        </a>
    </div>

    <?php if (isset($error_message)): ?>
    <div class="alert alert-danger">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($error_message) ?>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['success'])): ?>
    <div class="alert alert-success alert-dismissible fade show">
        <i class="fa fa-check-circle mr-2"></i><?= htmlspecialchars($_GET['success']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <?php if (isset($_GET['error'])): ?>
    <div class="alert alert-danger alert-dismissible fade show">
        <i class="fa fa-exclamation-circle mr-2"></i><?= htmlspecialchars($_GET['error']) ?>
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    </div>
    <?php endif; ?>

    <div class="card summary-card">
        <div class="card-header card-header-brand-primary">
            <i class="fa fa-briefcase mr-2"></i>Daftar Jabatan (<?= count($jabatan_list) ?> jabatan)
        </div>
        <div class="card-body p-0">
            <?php if (count($jabatan_list) > 0): ?>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead style="background: rgba(0,90,153,0.1);">
                        <tr>
                            <th><i class="fa fa-hashtag mr-1"></i>No</th>
                            <th><i class="fa fa-briefcase mr-1"></i>Nama Jabatan</th>
                            <th><i class="fa fa-info-circle mr-1"></i>Deskripsi</th>
                            <th><i class="fa fa-sort-numeric-asc mr-1"></i>Urutan</th>
                            <th><i class="fa fa-users mr-1"></i>Digunakan</th>
                            <th><i class="fa fa-calendar mr-1"></i>Dibuat</th>
                            <th><i class="fa fa-cogs mr-1"></i>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $no = 1; foreach ($jabatan_list as $jabatan): ?>
                        <tr>
                            <td><?= $no++ ?></td>
                            <td>
                                <strong><?= htmlspecialchars($jabatan['nama_jabatan']) ?></strong>
                                <?php if ($jabatan['is_active'] == 0): ?>
                                <span class="badge badge-secondary ml-2">Tidak Aktif</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?= htmlspecialchars(substr($jabatan['deskripsi'] ?? '', 0, 50)) ?>
                                <?= strlen($jabatan['deskripsi'] ?? '') > 50 ? '...' : '' ?>
                            </td>
                            <td>
                                <span class="badge badge-info"><?= $jabatan['urutan'] ?? '-' ?></span>
                            </td>
                            <td>
                                <?php
                                try {
                                    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM pengurus WHERE jabatan COLLATE utf8mb4_general_ci = ? COLLATE utf8mb4_general_ci");
                                    $stmt->execute([$jabatan['nama_jabatan']]);
                                    $usage_count = $stmt->fetchColumn();
                                    echo '<span class="badge badge-' . ($usage_count > 0 ? 'success' : 'light') . '">' . $usage_count . ' pengurus</span>';
                                } catch (Exception $e) {
                                    echo '<span class="badge badge-warning">Error</span>';
                                }
                                ?>
                            </td>
                            <td>
                                <?= isset($jabatan['created_at']) ? date('d/m/Y', strtotime($jabatan['created_at'])) : '-' ?>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="jabatan-edit.php?id=<?= $jabatan['id'] ?>" 
                                       class="btn btn-sm btn-warning" title="Edit">
                                        <i class="fa fa-edit"></i>
                                    </a>
                                    <?php
                                    // Check if jabatan is being used
                                    $stmt = $conn->prepare("SELECT COUNT(*) as count FROM pengurus WHERE jabatan = ?");
                                    $stmt->execute([$jabatan['nama_jabatan']]);
                                    $usage_count = $stmt->fetchColumn();
                                    ?>
                                    <?php if ($usage_count == 0): ?>
                                    <a href="jabatan.php?delete=<?= $jabatan['id'] ?>" 
                                       class="btn btn-sm btn-danger" 
                                       onclick="return confirm('Apakah Anda yakin ingin menghapus jabatan ini?')" 
                                       title="Hapus">
                                        <i class="fa fa-trash"></i>
                                    </a>
                                    <?php else: ?>
                                    <button class="btn btn-sm btn-secondary" 
                                            title="Tidak dapat dihapus karena sedang digunakan" disabled>
                                        <i class="fa fa-lock"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            <?php else: ?>
            <div class="text-center py-5">
                <i class="fa fa-briefcase" style="font-size: 4rem; color: #ccc;"></i>
                <h4 class="mt-3 text-muted">Belum ada jabatan</h4>
                <p class="text-muted">Klik tombol "Tambah Jabatan" untuk menambah jabatan baru.</p>
                <a href="jabatan-add.php" class="btn btn-brand-primary mt-3">
                    <i class="fa fa-plus mr-2"></i>Tambah Jabatan Pertama
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script src="../js/jquery-1.12.4.min.js"></script>
<script src="../js/bootstrap.min.js"></script>
<script>
$(document).ready(function() {
    // Auto hide alerts
    setTimeout(function() {
        $('.alert').fadeOut();
    }, 5000);
    
    // Add hover effects
    $('tbody tr').hover(
        function() { $(this).addClass('table-active'); },
        function() { $(this).removeClass('table-active'); }
    );
});
</script>

</body>
</html>
