<?php
ob_start();
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}
include_once("db_conn.php");
date_default_timezone_set('Asia/Jakarta');
$stmt = $conn->prepare("SELECT * FROM galeri ORDER BY tanggal DESC");
$stmt->execute();
$galeri = $stmt->fetchAll(PDO::FETCH_ASSOC);
?>
<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Galeri UKM Panahan USM</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css">
    <style>
        body {
            background: #fff !important;
            min-height: 100vh;
        }
        .galeri-section-title {
            font-family: '<PERSON><PERSON>', '<PERSON>o', sans-serif;
            font-size: 2.2rem;
            font-weight: 700;
            color: #0a2947;
            margin-bottom: 32px;
            text-align: center;
            letter-spacing: 1px;
            text-shadow: 0 2px 8px #ff980033;
        }
        .galeri-card {
            border: none;
            border-radius: 18px;
            box-shadow: 0 4px 24px 0 rgba(10,41,71,0.10);
            margin-bottom: 32px;
            background: #fff;
            transition: box-shadow 0.22s, transform 0.22s;
            overflow: hidden;
        }
        .galeri-card:hover {
            box-shadow: 0 12px 36px 0 rgba(10,41,71,0.18);
            transform: translateY(-4px) scale(1.025);
        }
        .galeri-img, .galeri-video {
            max-width: 100%;
            height: 220px;
            object-fit: cover;
            border-radius: 18px 18px 0 0;
            background: #000;
            box-shadow: 0 2px 8px #0a29471a;
        }
        .galeri-card .card-body {
            padding: 20px 20px 14px 20px;
        }
        .galeri-card .card-title {
            font-size: 1.18rem;
            font-weight: 700;
            color: #0a2947;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        }
        .galeri-card .card-text {
            font-size: 1.01rem;
            color: #444;
            margin-bottom: 0;
        }
        .galeri-card .card-footer {
            background: none;
            border: none;
            font-size: 0.92rem;
            color: #888;
            text-align: right;
        }
        .galeri-card-col { display: flex; }
        .galeri-card.h-100 { display: flex; flex-direction: column; height: 100%; }
    </style>
</head>
<body>
<?php include 'inc/navbar.php'; ?>
<!-- Modal untuk preview gambar -->
<div class="modal fade" id="galeriModal" tabindex="-1" role="dialog" aria-labelledby="galeriModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
    <div class="modal-content">
      <div class="modal-header border-0">
        <h5 class="modal-title" id="galeriModalLabel"></h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body text-center">
        <img id="galeriModalImg" src="" alt="" style="max-width:100%;max-height:70vh;border-radius:12px;box-shadow:0 4px 24px #0002;" />
      </div>
    </div>
  </div>
</div>
<div class="container py-4">
    <h2 class="galeri-section-title">Galeri UKM Panahan Universitas Semarang</h2>
    <div class="row">
        <?php foreach($galeri as $g): ?>
        <div class="col-md-4 col-sm-6 galeri-card-col">
            <div class="card galeri-card h-100">
                <?php
                $gambar = $g['gambar'] ?? '';
                $ext = strtolower(pathinfo($gambar, PATHINFO_EXTENSION));
                $isVideo = in_array($ext, ['mp4','webm','ogg']);
                if ($isVideo) {
                    echo '<video class="galeri-video" controls><source src="upload/galeri/'.htmlspecialchars($gambar).'" type="video/mp4">Video tidak didukung.</video>';
                } else {
                    echo '<img src="upload/galeri/'.htmlspecialchars($gambar).'" class="galeri-img card-img-top galeri-img-popup" alt="'.htmlspecialchars($g['judul']).'" data-judul="'.htmlspecialchars($g['judul']).'">';
                }
                ?>
                <div class="card-body">
                    <h5 class="card-title"><?=htmlspecialchars($g['judul'])?></h5>
                    <?php if (!empty($g['deskripsi'])): ?>
                        <p class="card-text"><?=htmlspecialchars($g['deskripsi'])?></p>
                    <?php endif; ?>
                </div>
                <div class="card-footer text-muted">
                    Diposting: <?= isset($g['tanggal']) ? date('d M Y', strtotime($g['tanggal'])) : '' ?>
                </div>
            </div>
        </div>
        <?php endforeach; ?>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Pastikan dropdown Bootstrap berjalan
$(function(){
  $('.dropdown-toggle').dropdown();
  // Popup gambar galeri
  $('.galeri-img-popup').on('click', function(){
    var src = $(this).attr('src');
    var judul = $(this).data('judul') || '';
    $('#galeriModalImg').attr('src', src);
    $('#galeriModalLabel').text(judul);
    $('#galeriModal').modal('show');
  });
});
</script>
</body>
<footer><?php include 'inc/footer.php'; ?></footer>
</html>
