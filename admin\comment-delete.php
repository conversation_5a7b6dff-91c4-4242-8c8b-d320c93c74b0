<?php
session_start();

// Check if user is logged in
if (!isset($_SESSION['username'])) {
    header('Location: ../login.php');
    exit();
}

// Include database connection
include_once __DIR__ . '/../db_conn.php';

// Check if ID is provided
if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
    header('Location: comment.php?error=' . urlencode('ID komentar tidak valid'));
    exit();
}

$comment_id = intval($_GET['id']);

try {
    // Check if comment exists
    $stmt = $conn->prepare("SELECT * FROM comments WHERE id = ?");
    $stmt->execute([$comment_id]);
    $comment = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$comment) {
        header('Location: comment.php?error=' . urlencode('Komentar tidak ditemukan'));
        exit();
    }

    // Delete comment
    $stmt = $conn->prepare("DELETE FROM comments WHERE id = ?");
    $result = $stmt->execute([$comment_id]);

    if ($result) {
        header('Location: comment.php?success=' . urlencode('Komentar berhasil dihapus'));
    } else {
        header('Location: comment.php?error=' . urlencode('Gagal menghapus komentar'));
    }

} catch (Exception $e) {
    header('Location: comment.php?error=' . urlencode('Error: ' . $e->getMessage()));
}

exit();
